(()=>{var e={};e.id=754,e.ids=[754],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5651:()=>{},5979:()=>{},8934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>p});var n=t(6639),o=t(9880),a=t(7991),i=t(1246);async function p(e){try{let r=await e.json(),t=await u(r);return i.NextResponse.json(t)}catch(e){return console.error("Session API error:",e),i.NextResponse.json({error:"Failed to process session"},{status:500})}}async function u(e){let{userName:r,startupName:t,transcript:s,duration:n}=e,o=s.map(e=>`${"user"===e.speaker?r:"VC"}: ${e.message}`).join("\n"),a=s.filter(e=>"user"===e.speaker).reduce((e,r)=>e+r.message.split(" ").length,0),i=Math.min(95,Math.max(60,70+3*s.filter(e=>"user"===e.speaker).length)),p=Math.min(95,Math.max(60,65+a/10)),u=Math.min(95,Math.max(60,75+20*Math.random())),d=Math.round((i+p+u)/3),c=[],l=[],h=[];return i>=80?l.push("Confident delivery and strong presence"):h.push("Work on building confidence in your delivery"),p>=80?l.push("Clear and articulate communication"):h.push("Focus on clearer articulation of key points"),u>=80?l.push("Strong vision and market understanding"):h.push("Develop a clearer long-term vision"),c.push("Good engagement with the interviewer"),c.push("Demonstrated understanding of the market"),n<300&&h.push("Consider providing more detailed responses"),{transcript:o,summary:`${r} presented ${t} with ${d>=80?"strong":d>=60?"good":"developing"} performance. The pitch demonstrated ${i>=80?"high":"moderate"} confidence levels and ${p>=80?"excellent":"good"} clarity in communication. ${u>=80?"The vision was compelling and well-articulated.":"The vision could be strengthened with more specific details."}`,scores:{confidence:i,clarity:p,vision:u,overall:d},feedback:c,strengths:l,improvements:h}}let d=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/session/route",pathname:"/api/session",filename:"route",bundlePath:"app/api/session/route"},resolvedPagePath:"/Users/<USER>/Downloads/pitchpal/src/app/api/session/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:h}=d;function m(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[31,714],()=>t(8934));module.exports=s})();