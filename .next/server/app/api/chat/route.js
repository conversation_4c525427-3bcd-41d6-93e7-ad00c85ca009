(()=>{var e={};e.id=276,e.ids=[276],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5651:()=>{},5979:()=>{},6362:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>h});var o={};r.r(o),r.d(o,{POST:()=>p});var a=r(6639),s=r(9880),n=r(7991),i=r(1246);let u={1:"You are a venture capitalist conducting an initial interview. Ask icebreaker questions about the startup's core concept and problem they're solving. Be friendly but professional.",2:"You are a VC now diving into market questions. Ask about market size, target users, and market validation. Be more analytical.",3:"You are a VC focusing on product questions. Ask about timing, technology, and defensibility. Be more technical and probing.",4:"You are a VC asking business model questions. Focus on moats, competition, and business strategy. Be more challenging.",5:"You are a VC delivering gut punch questions. Challenge the fundamental premise of the business. Be tough but fair."},l={1:["Tell me what you're building.","What's the problem you solve?","How did you come up with this idea?"],2:["How big is this market really?","Who's your target user?","How do you know people want this?"],3:["Why now?","What makes your tech defensible?","What's your unfair advantage?"],4:["What's your moat?","What if a major competitor enters the space?","How do you plan to make money?"],5:["This sounds like a feature, not a company. Convince me otherwise.","Why won't this be commoditized in 2 years?","What if Google builds this tomorrow?"]};async function p(e){try{let{messages:t,currentLevel:r,userName:o,startupName:a}=await e.json();u[r];let s=l[r],n="";if(1===t.length)n=`Hello ${o}, I'm excited to hear about ${a}. ${s[0]}`;else{let e=s[Math.floor(Math.random()*s.length)];n=`That's interesting. ${e}`}return i.NextResponse.json({message:n,nextLevel:r<5?r+1:5,shouldEscalate:Math.random()>.7&&r<5})}catch(e){return console.error("Chat API error:",e),i.NextResponse.json({error:"Failed to process chat request"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/chat/route",pathname:"/api/chat",filename:"route",bundlePath:"app/api/chat/route"},resolvedPagePath:"/Users/<USER>/Downloads/pitchpal/src/app/api/chat/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:c,workUnitAsyncStorage:h,serverHooks:m}=d;function g(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:h})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[31,714],()=>r(6362));module.exports=o})();