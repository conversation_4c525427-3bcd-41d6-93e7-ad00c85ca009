(()=>{var e={};e.id=528,e.ids=[528],e.modules={365:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>u});var o=t(6639),i=t(9880),n=t(7991),a=t(1246);async function u(e){try{let{text:r,voice_id:t="your-chris-voice-id"}=await e.json();if(!r)return a.NextResponse.json({error:"Text is required"},{status:400});return a.NextResponse.json({success:!0,message:"Voice synthesis would be processed here",audioUrl:"/api/voice/mock-audio",duration:.1*r.length})}catch(e){return console.error("Voice API error:",e),a.NextResponse.json({error:"Failed to synthesize voice"},{status:500})}}async function p(){return a.NextResponse.json({message:"Mock audio endpoint - would serve actual audio in production"})}let c=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/voice/route",pathname:"/api/voice",filename:"route",bundlePath:"app/api/voice/route"},resolvedPagePath:"/Users/<USER>/Downloads/pitchpal/src/app/api/voice/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function v(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5651:()=>{},5979:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[31,714],()=>t(365));module.exports=s})();