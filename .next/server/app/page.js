(()=>{var e={};e.id=974,e.ids=[974],e.modules={704:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2796,23)),Promise.resolve().then(r.t.bind(r,7514,23)),Promise.resolve().then(r.t.bind(r,4858,23)),Promise.resolve().then(r.t.bind(r,7893,23)),Promise.resolve().then(r.t.bind(r,3825,23)),Promise.resolve().then(r.t.bind(r,169,23)),Promise.resolve().then(r.t.bind(r,5889,23)),Promise.resolve().then(r.t.bind(r,4235,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1876:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4508:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(5547).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Downloads/pitchpal/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/pitchpal/src/app/page.tsx","default")},5552:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5674,23)),Promise.resolve().then(r.t.bind(r,1364,23)),Promise.resolve().then(r.t.bind(r,1432,23)),Promise.resolve().then(r.t.bind(r,1939,23)),Promise.resolve().then(r.t.bind(r,2067,23)),Promise.resolve().then(r.t.bind(r,7547,23)),Promise.resolve().then(r.t.bind(r,2171,23)),Promise.resolve().then(r.t.bind(r,7813,23))},5555:()=>{},5646:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>d});var i=r(6855),a=r(9880),s=r(4858),n=r.n(s),o=r(4637),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4508)),"/Users/<USER>/Downloads/pitchpal/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,8140))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8071)),"/Users/<USER>/Downloads/pitchpal/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9254,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,5007,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,2260,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,8140))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["/Users/<USER>/Downloads/pitchpal/src/app/page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5761:(e,t,r)=>{Promise.resolve().then(r.bind(r,4508))},7908:()=>{},8071:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var i=r(4789),a=r(978),s=r.n(a),n=r(8980),o=r.n(n);r(5555);let l={title:"Pitchpal - Practice Your VC Pitch",description:"Simulate venture capital interviews and improve your pitch with AI-powered feedback"};function d({children:e}){return(0,i.jsx)("html",{lang:"en",children:(0,i.jsx)("body",{className:`${s().variable} ${o().variable} antialiased`,children:e})})}},8140:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var i=r(5130);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},8218:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var i=r(1551),a=r(794),s=r(1965),n=r(3491);function o(){let[e,t]=(0,a.useState)(""),[r,o]=(0,a.useState)(""),[l,d]=(0,a.useState)(!1),p=(0,s.useRouter)(),c=async t=>{t.preventDefault(),e.trim()&&r.trim()&&(d(!0),sessionStorage.setItem("pitchpal_user",JSON.stringify({name:e.trim(),startupName:r.trim()})),p.push("/call"))};return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4",children:(0,i.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"max-w-md w-full",children:[(0,i.jsxs)("div",{className:"text-center mb-8",children:[(0,i.jsx)(n.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"text-4xl font-bold text-white mb-4",children:"Pitchpal"}),(0,i.jsx)(n.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-gray-300 text-lg",children:"Practice your VC pitch with AI-powered feedback"})]}),(0,i.jsx)(n.P.form,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},onSubmit:c,className:"bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20",children:(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-200 mb-2",children:"Your Name"}),(0,i.jsx)("input",{type:"text",id:"name",value:e,onChange:e=>t(e.target.value),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Enter your name",required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"startup",className:"block text-sm font-medium text-gray-200 mb-2",children:"Startup Name"}),(0,i.jsx)("input",{type:"text",id:"startup",value:r,onChange:e=>o(e.target.value),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Enter your startup name",required:!0})]}),(0,i.jsx)(n.P.button,{type:"submit",disabled:l||!e.trim()||!r.trim(),whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold py-3 px-6 rounded-lg hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:l?"Starting...":"Start Your Pitch"})]})}),(0,i.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:1},className:"text-center mt-8 text-gray-400 text-sm",children:(0,i.jsx)("p",{children:"Get ready for a realistic VC interview simulation"})})]})})}},8913:(e,t,r)=>{Promise.resolve().then(r.bind(r,8218))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[31,575,620],()=>r(5646));module.exports=i})();