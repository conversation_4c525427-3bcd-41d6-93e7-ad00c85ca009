(()=>{var e={};e.id=239,e.ids=[239],e.modules={484:(e,t,s)=>{Promise.resolve().then(s.bind(s,8916))},704:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2796,23)),Promise.resolve().then(s.t.bind(s,7514,23)),Promise.resolve().then(s.t.bind(s,4858,23)),Promise.resolve().then(s.t.bind(s,7893,23)),Promise.resolve().then(s.t.bind(s,3825,23)),Promise.resolve().then(s.t.bind(s,169,23)),Promise.resolve().then(s.t.bind(s,5889,23)),Promise.resolve().then(s.t.bind(s,4235,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1876:()=>{},2868:(e,t,s)=>{Promise.resolve().then(s.bind(s,8347))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3316:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=s(6855),i=s(9880),n=s(4858),a=s.n(n),o=s(4637),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["call",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8347)),"/Users/<USER>/Downloads/pitchpal/src/app/call/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,8140))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8071)),"/Users/<USER>/Downloads/pitchpal/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9254,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,5007,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,2260,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,8140))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Downloads/pitchpal/src/app/call/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/call/page",pathname:"/call",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3873:e=>{"use strict";e.exports=require("path")},5552:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,5674,23)),Promise.resolve().then(s.t.bind(s,1364,23)),Promise.resolve().then(s.t.bind(s,1432,23)),Promise.resolve().then(s.t.bind(s,1939,23)),Promise.resolve().then(s.t.bind(s,2067,23)),Promise.resolve().then(s.t.bind(s,7547,23)),Promise.resolve().then(s.t.bind(s,2171,23)),Promise.resolve().then(s.t.bind(s,7813,23))},5555:()=>{},7908:()=>{},8071:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>l});var r=s(4789),i=s(978),n=s.n(i),a=s(8980),o=s.n(a);s(5555);let l={title:"Pitchpal - Practice Your VC Pitch",description:"Simulate venture capital interviews and improve your pitch with AI-powered feedback"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${o().variable} antialiased`,children:e})})}},8140:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(5130);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},8347:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(5547).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Downloads/pitchpal/src/app/call/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/pitchpal/src/app/call/page.tsx","default")},8916:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(1551),i=s(794),n=s(1965),a=s(2782),o=s(890),l=s(3801),c=s(3698),d=s(6020),u=s(1907);class h extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,s=(0,d.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=s-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p({children:e,isPresent:t,anchorX:s}){let n=(0,i.useId)(),a=(0,i.useRef)(null),o=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,i.useContext)(u.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:r,top:i,left:c,right:d}=o.current;if(t||!a.current||!e||!r)return;let u="left"===s?`left: ${c}`:`right: ${d}`;a.current.dataset.motionPopId=n;let h=document.createElement("style");return l&&(h.nonce=l),document.head.appendChild(h),h.sheet&&h.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            ${u}px !important;
            top: ${i}px !important;
          }
        `),()=>{document.head.contains(h)&&document.head.removeChild(h)}},[t]),(0,r.jsx)(h,{isPresent:t,childRef:a,sizeRef:o,children:i.cloneElement(e,{ref:a})})}let m=({children:e,initial:t,isPresent:s,onExitComplete:n,custom:a,presenceAffectsLayout:l,mode:d,anchorX:u})=>{let h=(0,o.M)(x),m=(0,i.useId)(),f=!0,g=(0,i.useMemo)(()=>(f=!1,{id:m,initial:t,isPresent:s,custom:a,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;n&&n()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[s,h,n]);return l&&f&&(g={...g}),(0,i.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[s]),i.useEffect(()=>{s||h.size||!n||n()},[s]),"popLayout"===d&&(e=(0,r.jsx)(p,{isPresent:s,anchorX:u,children:e})),(0,r.jsx)(c.t.Provider,{value:g,children:e})};function x(){return new Map}var f=s(8441);let g=e=>e.key||"";function b(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let y=({children:e,custom:t,initial:s=!0,onExitComplete:n,presenceAffectsLayout:c=!0,mode:d="sync",propagate:u=!1,anchorX:h="left"})=>{let[p,x]=(0,f.xQ)(u),y=(0,i.useMemo)(()=>b(e),[e]),v=u&&!p?[]:y.map(g),w=(0,i.useRef)(!0),j=(0,i.useRef)(y),P=(0,o.M)(()=>new Map),[k,N]=(0,i.useState)(y),[S,C]=(0,i.useState)(y);(0,l.E)(()=>{w.current=!1,j.current=y;for(let e=0;e<S.length;e++){let t=g(S[e]);v.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[S,v.length,v.join("-")]);let E=[];if(y!==k){let e=[...y];for(let t=0;t<S.length;t++){let s=S[t],r=g(s);v.includes(r)||(e.splice(t,0,s),E.push(s))}return"wait"===d&&E.length&&(e=E),C(b(e)),N(y),null}let{forceRender:A}=(0,i.useContext)(a.L);return(0,r.jsx)(r.Fragment,{children:S.map(e=>{let i=g(e),a=(!u||!!p)&&(y===S||v.includes(i));return(0,r.jsx)(m,{isPresent:a,initial:(!w.current||!!s)&&void 0,custom:t,presenceAffectsLayout:c,mode:d,onExitComplete:a?void 0:()=>{if(!P.has(i))return;P.set(i,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(A?.(),C(j.current),u&&x?.(),n&&n())},anchorX:h,children:e},i)})})};var v=s(3491);function w({isActive:e,isListening:t,isAISpeaking:s,currentQuestion:n,onStartCall:a,onEndCall:o}){let l=(0,i.useRef)(null),[c,d]=(0,i.useState)(null),[u,h]=(0,i.useState)(null),p=async()=>{try{let e=await navigator.mediaDevices.getUserMedia({video:{width:{ideal:1280},height:{ideal:720},facingMode:"user"},audio:{echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}});d(e),h(!0),l.current&&(l.current.srcObject=e)}catch(e){console.error("Error accessing camera:",e),h(!1)}};return!1===u?(0,r.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-white",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCF9"}),(0,r.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Camera Access Required"}),(0,r.jsx)("p",{className:"text-gray-400 mb-6",children:"Please allow camera and microphone access to start your pitch practice."}),(0,r.jsx)("button",{onClick:p,className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg",children:"Grant Access"})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-black flex flex-col",children:[(0,r.jsxs)("div",{className:"flex-1 flex",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)("video",{ref:l,autoPlay:!0,muted:!0,playsInline:!0,className:"w-full h-full object-cover"}),(0,r.jsx)("div",{className:"absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1 rounded-lg backdrop-blur-sm",children:"You"}),(0,r.jsx)(y,{children:t&&(0,r.jsxs)(v.P.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.8,opacity:0},className:"absolute top-4 left-4 bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-2",children:[(0,r.jsx)(v.P.div,{animate:{scale:[1,1.2,1]},transition:{repeat:1/0,duration:1},className:"w-2 h-2 bg-white rounded-full"}),(0,r.jsx)("span",{children:"Listening..."})]})})]}),(0,r.jsxs)("div",{className:"flex-1 relative bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center",children:[(0,r.jsxs)("div",{className:"text-center text-white",children:[(0,r.jsx)(v.P.div,{animate:s?{scale:[1,1.05,1]}:{},transition:{repeat:s?1/0:0,duration:2},className:"w-40 h-40 bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center mb-6 mx-auto shadow-2xl",children:(0,r.jsx)("span",{className:"text-6xl font-bold",children:"K"})}),(0,r.jsx)("h3",{className:"text-2xl font-semibold mb-2",children:"Kenard (CEO)"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Venture Capital Partner"}),(0,r.jsx)(y,{children:s&&(0,r.jsxs)(v.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},className:"flex items-center justify-center space-x-1",children:[(0,r.jsx)("div",{className:"text-green-400 text-sm font-medium",children:"Speaking"}),(0,r.jsx)(v.P.div,{animate:{scale:[1,1.2,1]},transition:{repeat:1/0,duration:.8},className:"w-2 h-2 bg-green-400 rounded-full"})]})})]}),(0,r.jsx)("div",{className:"absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded-lg backdrop-blur-sm",children:"Kenard (CEO)"})]})]}),(0,r.jsx)(y,{children:n&&e&&(0,r.jsx)(v.P.div,{initial:{y:100,opacity:0},animate:{y:0,opacity:1},exit:{y:100,opacity:0},className:"bg-gray-900/95 backdrop-blur-sm p-6 border-t border-gray-700",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto text-center",children:(0,r.jsx)("p",{className:"text-white text-lg font-medium",children:n})})})}),(0,r.jsx)(y,{children:!e&&u&&(0,r.jsx)(v.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)(v.P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},transition:{delay:.2},className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"Ready to Start Your Pitch?"}),(0,r.jsx)("p",{className:"text-gray-300 text-lg",children:"You'll be interviewed by Kenard, an experienced VC partner"})]}),(0,r.jsx)(v.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:a,className:"bg-green-600 hover:bg-green-700 text-white text-xl font-semibold px-12 py-4 rounded-xl shadow-lg transition-colors",children:"Start Call"})]})})})]})}function j({isListening:e,onSpeechResult:t,onSpeechEnd:s,onError:n}){(0,i.useRef)(null);let[a,o]=(0,i.useState)(!1),[l,c]=(0,i.useState)("");return((0,i.useCallback)(async e=>new Promise((t,s)=>{if(!("speechSynthesis"in window))return void s(Error("Speech synthesis not supported"));window.speechSynthesis.cancel();let r=new SpeechSynthesisUtterance(e);r.rate=.9,r.pitch=1,r.volume=1;let i=window.speechSynthesis.getVoices().find(e=>e.name.includes("Alex")||e.name.includes("Daniel")||e.name.includes("Google US English"));i&&(r.voice=i),r.onend=()=>{t()},r.onerror=e=>{s(Error(`Speech synthesis error: ${e.error}`))},window.speechSynthesis.speak(r)}),[]),a)?(0,r.jsx)(r.Fragment,{children:l&&e&&(0,r.jsxs)("div",{className:"fixed bottom-20 left-4 right-4 bg-black/80 text-white p-4 rounded-lg backdrop-blur-sm",children:[(0,r.jsx)("div",{className:"text-sm text-gray-400 mb-1",children:"You're saying:"}),(0,r.jsx)("div",{className:"text-white",children:l})]})}):(0,r.jsx)("div",{className:"fixed bottom-4 right-4 bg-red-600 text-white p-4 rounded-lg shadow-lg",children:(0,r.jsx)("p",{className:"text-sm",children:"Speech recognition is not supported in this browser. Please use Chrome, Edge, or Safari for the best experience."})})}async function P(e){try{let t=await fetch("/api/voice",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:e})});if(!t.ok)throw Error(`Voice synthesis failed: ${t.statusText}`);let s=await t.json();if(s.audioUrl)return s.audioUrl;if("speechSynthesis"in window)return new Promise((t,s)=>{let r=new SpeechSynthesisUtterance(e);r.onend=()=>t("browser-synthesis"),r.onerror=()=>s(Error("Browser synthesis failed")),window.speechSynthesis.speak(r)});throw Error("No speech synthesis available")}catch(e){throw console.error("ElevenLabs synthesis error:",e),e}}let k=[{level:1,title:"Icebreakers",description:"Getting to know your startup and the problem you're solving",questions:["Tell me what you're building.","What's the problem you solve?","How did you come up with this idea?"]},{level:2,title:"Market",description:"Understanding your market and target audience",questions:["How big is this market really?","Who's your target user?","How do you know people want this?","What's your go-to-market strategy?"]},{level:3,title:"Product",description:"Diving into your product and technology",questions:["Why now?","What makes your tech defensible?","What's your unfair advantage?","How does your product work?"]},{level:4,title:"Business",description:"Exploring your business model and strategy",questions:["What's your moat?","What if a major competitor enters the space?","How do you plan to make money?","What are your unit economics?"]},{level:5,title:"Gut Punch",description:"Challenging questions to test your resilience",questions:["This sounds like a feature, not a company. Convince me otherwise.","Why won't this be commoditized in 2 years?","What if Google builds this tomorrow?","Why should I invest in you versus the 100 other startups I see?"]}];function N(){let[e,t]=(0,i.useState)(null),[s,a]=(0,i.useState)({isActive:!1,currentLevel:1,isListening:!1,isAISpeaking:!1,transcript:[],currentQuestion:"",sessionId:`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}),[o,l]=(0,i.useState)(""),c=(0,n.useRouter)(),d=async()=>{if(!e)return;a(e=>({...e,isActive:!0}));let t=`Hello ${e.name}, I'm excited to hear about ${e.startupName}. ${k[0].questions[0]}`;a(e=>({...e,currentQuestion:t,isAISpeaking:!0}));let s={speaker:"ai",message:t,timestamp:Date.now()};a(e=>({...e,transcript:[...e.transcript,s]})),await u(t)},u=async e=>{try{await P(e)}catch(t){if(console.error("Voice synthesis error:",t),"speechSynthesis"in window){let t=new SpeechSynthesisUtterance(e);window.speechSynthesis.speak(t)}}setTimeout(()=>{a(e=>({...e,isAISpeaking:!1,isListening:!0}))},3e3)},h=async t=>{if(!e)return;let r={speaker:"user",message:t,timestamp:Date.now()};a(e=>({...e,transcript:[...e.transcript,r],isListening:!1}));try{let r=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[{role:"user",content:t}],currentLevel:s.currentLevel,userName:e.name,startupName:e.startupName})}),i=await r.json();if(i.message){a(e=>({...e,currentQuestion:i.message,isAISpeaking:!0,currentLevel:i.shouldEscalate?i.nextLevel:e.currentLevel}));let e={speaker:"ai",message:i.message,timestamp:Date.now()};a(t=>({...t,transcript:[...t.transcript,e]})),await u(i.message)}}catch(e){console.error("Chat API error:",e),l("Failed to get AI response")}},p=async()=>{if(!e)return;let t={userName:e.name,startupName:e.startupName,transcript:s.transcript,duration:Math.floor((Date.now()-(s.transcript[0]?.timestamp||Date.now()))/1e3),startTime:s.transcript[0]?.timestamp||Date.now(),endTime:Date.now()};try{let e=await fetch("/api/session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),s=await e.json();sessionStorage.setItem("pitchpal_results",JSON.stringify(s))}catch(e){console.error("Session save error:",e)}a(e=>({...e,isActive:!1})),c.push("/results")};return e?(0,r.jsxs)("div",{className:"min-h-screen bg-black flex flex-col",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-4 bg-gray-900/50 backdrop-blur-sm z-10",children:[(0,r.jsxs)("div",{className:"text-white",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold",children:e.name}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:e.startupName})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[s.isActive&&(0,r.jsxs)("div",{className:"text-white text-sm",children:["Level ",s.currentLevel,": ",k[s.currentLevel-1]?.title]}),(0,r.jsx)("button",{onClick:p,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors",children:"End Call"})]})]}),o&&(0,r.jsxs)("div",{className:"bg-red-600 text-white p-3 text-center",children:[o,(0,r.jsx)("button",{onClick:()=>l(""),className:"ml-4 underline",children:"Dismiss"})]}),(0,r.jsx)(w,{isActive:s.isActive,isListening:s.isListening,isAISpeaking:s.isAISpeaking,currentQuestion:s.currentQuestion,onStartCall:d,onEndCall:p}),(0,r.jsx)(j,{isListening:s.isListening,onSpeechResult:h,onSpeechEnd:()=>{},onError:e=>{l(e)}})]}):(0,r.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-white text-xl",children:"Loading..."})})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[31,575,620],()=>s(3316));module.exports=r})();