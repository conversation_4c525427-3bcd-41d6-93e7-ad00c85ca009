(()=>{var e={};e.id=139,e.ids=[139],e.modules={704:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2796,23)),Promise.resolve().then(r.t.bind(r,7514,23)),Promise.resolve().then(r.t.bind(r,4858,23)),Promise.resolve().then(r.t.bind(r,7893,23)),Promise.resolve().then(r.t.bind(r,3825,23)),Promise.resolve().then(r.t.bind(r,169,23)),Promise.resolve().then(r.t.bind(r,5889,23)),Promise.resolve().then(r.t.bind(r,4235,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1674:(e,t,r)=>{Promise.resolve().then(r.bind(r,5342))},1842:(e,t,r)=>{Promise.resolve().then(r.bind(r,8751))},1876:()=>{},2890:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=r(6855),i=r(9880),a=r(4858),n=r.n(a),l=r(4637),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["results",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5342)),"/Users/<USER>/Downloads/pitchpal/src/app/results/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,8140))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8071)),"/Users/<USER>/Downloads/pitchpal/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9254,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,5007,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,2260,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,8140))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Downloads/pitchpal/src/app/results/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/results/page",pathname:"/results",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5342:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(5547).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Downloads/pitchpal/src/app/results/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/pitchpal/src/app/results/page.tsx","default")},5552:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5674,23)),Promise.resolve().then(r.t.bind(r,1364,23)),Promise.resolve().then(r.t.bind(r,1432,23)),Promise.resolve().then(r.t.bind(r,1939,23)),Promise.resolve().then(r.t.bind(r,2067,23)),Promise.resolve().then(r.t.bind(r,7547,23)),Promise.resolve().then(r.t.bind(r,2171,23)),Promise.resolve().then(r.t.bind(r,7813,23))},5555:()=>{},7908:()=>{},8071:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>o});var s=r(4789),i=r(978),a=r.n(i),n=r(8980),l=r.n(n);r(5555);let o={title:"Pitchpal - Practice Your VC Pitch",description:"Simulate venture capital interviews and improve your pitch with AI-powered feedback"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${a().variable} ${l().variable} antialiased`,children:e})})}},8140:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(5130);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},8751:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(1551),i=r(794),a=r(1965),n=r(3491);function l(){let[e,t]=(0,i.useState)(null),[r,l]=(0,i.useState)(null),[o,d]=(0,i.useState)(!0),c=(0,a.useRouter)();return!e||o?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center",children:(0,s.jsx)(n.P.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full"})}):r?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-white mb-4",children:"Pitch Results"}),(0,s.jsxs)("p",{className:"text-gray-300 text-lg",children:["Great job, ",e.name,"! Here's how your ",e.startupName," pitch performed."]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,s.jsxs)(n.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-6",children:"Your Scores"}),(0,s.jsx)("div",{className:"space-y-4",children:Object.entries(r.scores).map(([e,t])=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between text-white mb-2",children:[(0,s.jsx)("span",{className:"capitalize",children:e}),(0,s.jsxs)("span",{className:"font-semibold",children:[t,"/100"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-3",children:(0,s.jsx)(n.P.div,{initial:{width:0},animate:{width:`${t}%`},transition:{duration:1,delay:.5},className:`h-3 rounded-full ${t>=80?"bg-green-500":t>=60?"bg-yellow-500":"bg-red-500"}`})})]},e))})]}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.8,delay:.4},className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:"Summary"}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed",children:r.summary})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:"Strengths"}),(0,s.jsx)("ul",{className:"space-y-2",children:r.strengths.map((e,t)=>(0,s.jsxs)("li",{className:"text-green-400 flex items-start",children:[(0,s.jsx)("span",{className:"mr-2",children:"✓"}),(0,s.jsx)("span",{children:e})]},t))})]}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:"Areas for Improvement"}),(0,s.jsx)("ul",{className:"space-y-2",children:r.improvements.map((e,t)=>(0,s.jsxs)("li",{className:"text-yellow-400 flex items-start",children:[(0,s.jsx)("span",{className:"mr-2",children:"→"}),(0,s.jsx)("span",{children:e})]},t))})]})]}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1},className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)("button",{onClick:()=>{let e=`Just practiced my VC pitch with Pitchpal! 🚀

Overall Score: ${r?.scores.overall}/100
• Confidence: ${r?.scores.confidence}/100
• Clarity: ${r?.scores.clarity}/100
• Vision: ${r?.scores.vision}/100

Ready to pitch to real investors! 💪`;navigator.share?navigator.share({title:"My Pitchpal Results",text:e,url:window.location.origin}):(navigator.clipboard.writeText(e),alert("Results copied to clipboard!"))},className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-3 px-8 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200",children:"Share Results"}),(0,s.jsx)("button",{onClick:()=>{sessionStorage.removeItem("pitchpal_user"),c.push("/")},className:"bg-white/10 border border-white/20 text-white font-semibold py-3 px-8 rounded-lg hover:bg-white/20 transition-all duration-200",children:"Practice Again"})]})]})}):null}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[31,575,620],()=>r(2890));module.exports=s})();