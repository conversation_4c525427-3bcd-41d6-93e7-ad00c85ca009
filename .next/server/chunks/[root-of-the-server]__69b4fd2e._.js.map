{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/app/api/chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\ninterface ChatMessage {\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n}\n\ninterface ChatRequest {\n  messages: ChatMessage[];\n  currentLevel: number;\n  userName: string;\n  startupName: string;\n}\n\nconst ESCALATION_PROMPTS = {\n  1: \"You are a venture capitalist conducting an initial interview. Ask icebreaker questions about the startup's core concept and problem they're solving. Be friendly but professional.\",\n  2: \"You are a VC now diving into market questions. Ask about market size, target users, and market validation. Be more analytical.\",\n  3: \"You are a VC focusing on product questions. Ask about timing, technology, and defensibility. Be more technical and probing.\",\n  4: \"You are a VC asking business model questions. Focus on moats, competition, and business strategy. Be more challenging.\",\n  5: \"You are a VC delivering gut punch questions. Challenge the fundamental premise of the business. Be tough but fair.\"\n};\n\nconst LEVEL_QUESTIONS = {\n  1: [\n    \"Tell me what you're building.\",\n    \"What's the problem you solve?\",\n    \"How did you come up with this idea?\"\n  ],\n  2: [\n    \"How big is this market really?\",\n    \"Who's your target user?\",\n    \"How do you know people want this?\"\n  ],\n  3: [\n    \"Why now?\",\n    \"What makes your tech defensible?\",\n    \"What's your unfair advantage?\"\n  ],\n  4: [\n    \"What's your moat?\",\n    \"What if a major competitor enters the space?\",\n    \"How do you plan to make money?\"\n  ],\n  5: [\n    \"This sounds like a feature, not a company. Convince me otherwise.\",\n    \"Why won't this be commoditized in 2 years?\",\n    \"What if Google builds this tomorrow?\"\n  ]\n};\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { messages, currentLevel, userName, startupName }: ChatRequest = await request.json();\n\n    if (!process.env.OPENAI_API_KEY) {\n      throw new Error('OpenAI API key not configured');\n    }\n\n    const systemPrompt = ESCALATION_PROMPTS[currentLevel as keyof typeof ESCALATION_PROMPTS];\n    const levelQuestions = LEVEL_QUESTIONS[currentLevel as keyof typeof LEVEL_QUESTIONS];\n\n    // Build conversation context\n    const conversationMessages = [\n      {\n        role: 'system' as const,\n        content: `${systemPrompt}\n\nYou are interviewing ${userName} about their startup ${startupName}.\n\nGuidelines:\n- Ask one question at a time\n- Keep responses under 50 words\n- Be professional but conversational\n- Build on their previous answers\n- Use these level-appropriate questions as inspiration: ${levelQuestions.join(', ')}\n- Escalate difficulty naturally based on their responses`\n      },\n      ...messages\n    ];\n\n    const response = await fetch('https://api.openai.com/v1/chat/completions', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        model: 'gpt-4o-mini',\n        messages: conversationMessages,\n        max_tokens: 100,\n        temperature: 0.7,\n        presence_penalty: 0.1,\n        frequency_penalty: 0.1,\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);\n    }\n\n    const data = await response.json();\n    const aiResponse = data.choices[0].message.content;\n\n    // Determine if we should escalate based on conversation length and level\n    const shouldEscalate = messages.length >= 2 && currentLevel < 5 && Math.random() > 0.6;\n\n    return NextResponse.json({\n      message: aiResponse,\n      nextLevel: currentLevel < 5 ? currentLevel + 1 : 5,\n      shouldEscalate\n    });\n\n  } catch (error) {\n    console.error('Chat API error:', error);\n    return NextResponse.json(\n      { error: `Failed to process chat request: ${error instanceof Error ? error.message : 'Unknown error'}` },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAcA,MAAM,qBAAqB;IACzB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACL;AAEA,MAAM,kBAAkB;IACtB,GAAG;QACD;QACA;QACA;KACD;IACD,GAAG;QACD;QACA;QACA;KACD;IACD,GAAG;QACD;QACA;QACA;KACD;IACD,GAAG;QACD;QACA;QACA;KACD;IACD,GAAG;QACD;QACA;QACA;KACD;AACH;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAgB,MAAM,QAAQ,IAAI;QAEzF,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,eAAe,kBAAkB,CAAC,aAAgD;QACxF,MAAM,iBAAiB,eAAe,CAAC,aAA6C;QAEpF,6BAA6B;QAC7B,MAAM,uBAAuB;YAC3B;gBACE,MAAM;gBACN,SAAS,GAAG,aAAa;;qBAEZ,EAAE,SAAS,qBAAqB,EAAE,YAAY;;;;;;;wDAOX,EAAE,eAAe,IAAI,CAAC,MAAM;wDAC5B,CAAC;YACnD;eACG;SACJ;QAED,MAAM,WAAW,MAAM,MAAM,8CAA8C;YACzE,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE;gBACvD,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO;gBACP,UAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,kBAAkB;gBAClB,mBAAmB;YACrB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,UAAU,KAAK,EAAE,WAAW,SAAS,UAAU,EAAE;QACxF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,aAAa,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;QAElD,yEAAyE;QACzE,MAAM,iBAAiB,SAAS,MAAM,IAAI,KAAK,eAAe,KAAK,KAAK,MAAM,KAAK;QAEnF,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,WAAW,eAAe,IAAI,eAAe,IAAI;YACjD;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,CAAC,gCAAgC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAAC,GACvG;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}