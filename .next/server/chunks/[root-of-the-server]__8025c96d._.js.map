{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/app/api/session/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\ninterface SessionData {\n  userName: string;\n  startupName: string;\n  transcript: Array<{\n    speaker: 'user' | 'ai';\n    message: string;\n    timestamp: number;\n  }>;\n  duration: number;\n}\n\ninterface PitchScore {\n  confidence: number;\n  clarity: number;\n  vision: number;\n  overall: number;\n}\n\ninterface SessionResults {\n  transcript: string;\n  summary: string;\n  scores: PitchScore;\n  feedback: string[];\n  strengths: string[];\n  improvements: string[];\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const sessionData: SessionData = await request.json();\n\n    // Analyze the session and generate results\n    const results = await analyzeSession(sessionData);\n\n    return NextResponse.json(results);\n\n  } catch (error) {\n    console.error('Session API error:', error);\n    return NextResponse.json(\n      { error: 'Failed to process session' },\n      { status: 500 }\n    );\n  }\n}\n\nasync function analyzeSession(sessionData: SessionData): Promise<SessionResults> {\n  const { userName, startupName, transcript, duration } = sessionData;\n\n  // Generate transcript text\n  const transcriptText = transcript\n    .map(entry => `${entry.speaker === 'user' ? userName : 'VC'}: ${entry.message}`)\n    .join('\\n');\n\n  if (!process.env.OPENAI_API_KEY) {\n    throw new Error('OpenAI API key not configured');\n  }\n\n  try {\n    // Use GPT-4o-mini to analyze the pitch\n    const analysisPrompt = `You are an expert VC pitch analyst. Analyze this pitch transcript and provide detailed feedback.\n\nTranscript:\n${transcriptText}\n\nStartup: ${startupName}\nFounder: ${userName}\nDuration: ${Math.floor(duration / 60)} minutes ${duration % 60} seconds\n\nPlease provide your analysis in the following JSON format:\n{\n  \"confidence\": <score 0-100>,\n  \"clarity\": <score 0-100>,\n  \"vision\": <score 0-100>,\n  \"summary\": \"<2-3 sentence executive summary>\",\n  \"strengths\": [\"<strength 1>\", \"<strength 2>\", \"<strength 3>\"],\n  \"improvements\": [\"<improvement 1>\", \"<improvement 2>\", \"<improvement 3>\"],\n  \"feedback\": [\"<specific feedback 1>\", \"<specific feedback 2>\", \"<specific feedback 3>\"]\n}\n\nScoring criteria:\n- Confidence: Delivery, presence, conviction, handling of questions\n- Clarity: Communication effectiveness, structure, articulation\n- Vision: Strategic thinking, market understanding, long-term perspective\n\nBe specific and actionable in your feedback.`;\n\n    const response = await fetch('https://api.openai.com/v1/chat/completions', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        model: 'gpt-4o-mini',\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert VC pitch analyst. Provide detailed, actionable feedback in valid JSON format only.'\n          },\n          {\n            role: 'user',\n            content: analysisPrompt\n          }\n        ],\n        max_tokens: 1000,\n        temperature: 0.3,\n        response_format: { type: \"json_object\" }\n      }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`OpenAI API error: ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    const analysis = JSON.parse(data.choices[0].message.content);\n\n    // Calculate overall score\n    const overall = Math.round((analysis.confidence + analysis.clarity + analysis.vision) / 3);\n\n    return {\n      transcript: transcriptText,\n      summary: analysis.summary,\n      scores: {\n        confidence: analysis.confidence,\n        clarity: analysis.clarity,\n        vision: analysis.vision,\n        overall\n      },\n      feedback: analysis.feedback,\n      strengths: analysis.strengths,\n      improvements: analysis.improvements\n    };\n\n  } catch (error) {\n    console.error('AI analysis error:', error);\n\n    // Fallback to basic analysis if AI fails\n    const wordCount = transcript\n      .filter(entry => entry.speaker === 'user')\n      .reduce((count, entry) => count + entry.message.split(' ').length, 0);\n\n    const responseCount = transcript.filter(entry => entry.speaker === 'user').length;\n\n    const confidence = Math.min(95, Math.max(60, 70 + (responseCount * 3)));\n    const clarity = Math.min(95, Math.max(60, 65 + (wordCount / 10)));\n    const vision = Math.min(95, Math.max(60, 75 + Math.random() * 20));\n    const overall = Math.round((confidence + clarity + vision) / 3);\n\n    return {\n      transcript: transcriptText,\n      summary: `${userName} presented ${startupName} in a ${duration > 300 ? 'comprehensive' : 'brief'} pitch session. Analysis shows ${overall >= 80 ? 'strong' : 'developing'} performance across key metrics.`,\n      scores: { confidence, clarity, vision, overall },\n      feedback: [\"Good engagement with questions\", \"Clear communication style\", \"Room for improvement in specific areas\"],\n      strengths: [\"Active participation\", \"Responsive to questions\"],\n      improvements: [\"Provide more specific examples\", \"Strengthen key value propositions\"]\n    };\n  }\n}\n\n\n"], "names": [], "mappings": ";;;AAAA;;AA6BO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,cAA2B,MAAM,QAAQ,IAAI;QAEnD,2CAA2C;QAC3C,MAAM,UAAU,MAAM,eAAe;QAErC,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,eAAe,WAAwB;IACpD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;IAExD,2BAA2B;IAC3B,MAAM,iBAAiB,WACpB,GAAG,CAAC,CAAA,QAAS,GAAG,MAAM,OAAO,KAAK,SAAS,WAAW,KAAK,EAAE,EAAE,MAAM,OAAO,EAAE,EAC9E,IAAI,CAAC;IAER,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;QAC/B,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,uCAAuC;QACvC,MAAM,iBAAiB,CAAC;;;AAG5B,EAAE,eAAe;;SAER,EAAE,YAAY;SACd,EAAE,SAAS;UACV,EAAE,KAAK,KAAK,CAAC,WAAW,IAAI,SAAS,EAAE,WAAW,GAAG;;;;;;;;;;;;;;;;;;4CAkBnB,CAAC;QAEzC,MAAM,WAAW,MAAM,MAAM,8CAA8C;YACzE,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE;gBACvD,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,YAAY;gBACZ,aAAa;gBACb,iBAAiB;oBAAE,MAAM;gBAAc;YACzC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,SAAS,UAAU,EAAE;QAC5D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;QAE3D,0BAA0B;QAC1B,MAAM,UAAU,KAAK,KAAK,CAAC,CAAC,SAAS,UAAU,GAAG,SAAS,OAAO,GAAG,SAAS,MAAM,IAAI;QAExF,OAAO;YACL,YAAY;YACZ,SAAS,SAAS,OAAO;YACzB,QAAQ;gBACN,YAAY,SAAS,UAAU;gBAC/B,SAAS,SAAS,OAAO;gBACzB,QAAQ,SAAS,MAAM;gBACvB;YACF;YACA,UAAU,SAAS,QAAQ;YAC3B,WAAW,SAAS,SAAS;YAC7B,cAAc,SAAS,YAAY;QACrC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QAEpC,yCAAyC;QACzC,MAAM,YAAY,WACf,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,KAAK,QAClC,MAAM,CAAC,CAAC,OAAO,QAAU,QAAQ,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,MAAM,EAAE;QAErE,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,KAAK,QAAQ,MAAM;QAEjF,MAAM,aAAa,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAM,gBAAgB;QACnE,MAAM,UAAU,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAM,YAAY;QAC5D,MAAM,SAAS,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,MAAM,KAAK;QAC9D,MAAM,UAAU,KAAK,KAAK,CAAC,CAAC,aAAa,UAAU,MAAM,IAAI;QAE7D,OAAO;YACL,YAAY;YACZ,SAAS,GAAG,SAAS,WAAW,EAAE,YAAY,MAAM,EAAE,WAAW,MAAM,kBAAkB,QAAQ,+BAA+B,EAAE,WAAW,KAAK,WAAW,aAAa,gCAAgC,CAAC;YAC3M,QAAQ;gBAAE;gBAAY;gBAAS;gBAAQ;YAAQ;YAC/C,UAAU;gBAAC;gBAAkC;gBAA6B;aAAyC;YACnH,WAAW;gBAAC;gBAAwB;aAA0B;YAC9D,cAAc;gBAAC;gBAAkC;aAAoC;QACvF;IACF;AACF", "debugId": null}}]}