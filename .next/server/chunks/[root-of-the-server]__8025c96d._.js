module.exports = {

"[project]/.next-internal/server/app/api/session/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/session/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/server.js [app-route] (ecmascript)");
;
async function POST(request) {
    try {
        const sessionData = await request.json();
        // Analyze the session and generate results
        const results = await analyzeSession(sessionData);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(results);
    } catch (error) {
        console.error('Session API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to process session'
        }, {
            status: 500
        });
    }
}
async function analyzeSession(sessionData) {
    const { userName, startupName, transcript, duration } = sessionData;
    // Generate transcript text
    const transcriptText = transcript.map((entry)=>`${entry.speaker === 'user' ? userName : 'VC'}: ${entry.message}`).join('\n');
    if (!process.env.OPENAI_API_KEY) {
        throw new Error('OpenAI API key not configured');
    }
    try {
        // Use GPT-4o-mini to analyze the pitch
        const analysisPrompt = `You are an expert VC pitch analyst. Analyze this pitch transcript and provide detailed feedback.

Transcript:
${transcriptText}

Startup: ${startupName}
Founder: ${userName}
Duration: ${Math.floor(duration / 60)} minutes ${duration % 60} seconds

Please provide your analysis in the following JSON format:
{
  "confidence": <score 0-100>,
  "clarity": <score 0-100>,
  "vision": <score 0-100>,
  "summary": "<2-3 sentence executive summary>",
  "strengths": ["<strength 1>", "<strength 2>", "<strength 3>"],
  "improvements": ["<improvement 1>", "<improvement 2>", "<improvement 3>"],
  "feedback": ["<specific feedback 1>", "<specific feedback 2>", "<specific feedback 3>"]
}

Scoring criteria:
- Confidence: Delivery, presence, conviction, handling of questions
- Clarity: Communication effectiveness, structure, articulation
- Vision: Strategic thinking, market understanding, long-term perspective

Be specific and actionable in your feedback.`;
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: 'gpt-4o-mini',
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert VC pitch analyst. Provide detailed, actionable feedback in valid JSON format only.'
                    },
                    {
                        role: 'user',
                        content: analysisPrompt
                    }
                ],
                max_tokens: 1000,
                temperature: 0.3,
                response_format: {
                    type: "json_object"
                }
            })
        });
        if (!response.ok) {
            throw new Error(`OpenAI API error: ${response.statusText}`);
        }
        const data = await response.json();
        const analysis = JSON.parse(data.choices[0].message.content);
        // Calculate overall score
        const overall = Math.round((analysis.confidence + analysis.clarity + analysis.vision) / 3);
        return {
            transcript: transcriptText,
            summary: analysis.summary,
            scores: {
                confidence: analysis.confidence,
                clarity: analysis.clarity,
                vision: analysis.vision,
                overall
            },
            feedback: analysis.feedback,
            strengths: analysis.strengths,
            improvements: analysis.improvements
        };
    } catch (error) {
        console.error('AI analysis error:', error);
        // Fallback to basic analysis if AI fails
        const wordCount = transcript.filter((entry)=>entry.speaker === 'user').reduce((count, entry)=>count + entry.message.split(' ').length, 0);
        const responseCount = transcript.filter((entry)=>entry.speaker === 'user').length;
        const confidence = Math.min(95, Math.max(60, 70 + responseCount * 3));
        const clarity = Math.min(95, Math.max(60, 65 + wordCount / 10));
        const vision = Math.min(95, Math.max(60, 75 + Math.random() * 20));
        const overall = Math.round((confidence + clarity + vision) / 3);
        return {
            transcript: transcriptText,
            summary: `${userName} presented ${startupName} in a ${duration > 300 ? 'comprehensive' : 'brief'} pitch session. Analysis shows ${overall >= 80 ? 'strong' : 'developing'} performance across key metrics.`,
            scores: {
                confidence,
                clarity,
                vision,
                overall
            },
            feedback: [
                "Good engagement with questions",
                "Clear communication style",
                "Room for improvement in specific areas"
            ],
            strengths: [
                "Active participation",
                "Responsive to questions"
            ],
            improvements: [
                "Provide more specific examples",
                "Strengthen key value propositions"
            ]
        };
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8025c96d._.js.map