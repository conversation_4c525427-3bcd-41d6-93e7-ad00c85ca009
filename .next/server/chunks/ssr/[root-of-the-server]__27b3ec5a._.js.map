{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/app/results/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\n\ninterface UserData {\n  name: string;\n  startupName: string;\n}\n\ninterface PitchScore {\n  confidence: number;\n  clarity: number;\n  vision: number;\n  overall: number;\n}\n\ninterface PitchResults {\n  transcript: string;\n  summary: string;\n  scores: PitchScore;\n  feedback: string[];\n  strengths: string[];\n  improvements: string[];\n}\n\nexport default function ResultsPage() {\n  const [userData, setUserData] = useState<UserData | null>(null);\n  const [results, setResults] = useState<PitchResults | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const router = useRouter();\n\n  useEffect(() => {\n    // Get user data from sessionStorage\n    const storedData = sessionStorage.getItem('pitchpal_user');\n    if (!storedData) {\n      router.push('/');\n      return;\n    }\n    setUserData(JSON.parse(storedData));\n\n    // Get results from sessionStorage\n    const storedResults = sessionStorage.getItem('pitchpal_results');\n    if (storedResults) {\n      setResults(JSON.parse(storedResults));\n      setIsLoading(false);\n    } else {\n      // Fallback to mock results if no stored results\n      setTimeout(() => {\n        const userData = JSON.parse(storedData);\n        setResults({\n          transcript: \"Sample transcript of the conversation...\",\n          summary: `${userData.name} presented ${userData.startupName}, demonstrating strong vision and market understanding. The pitch showed good confidence levels with clear articulation of the problem and solution.`,\n          scores: {\n            confidence: 85,\n            clarity: 78,\n            vision: 92,\n            overall: 85\n          },\n          feedback: [\n            \"Strong opening with clear problem statement\",\n            \"Good market size understanding\",\n            \"Could improve on competitive analysis\"\n          ],\n          strengths: [\n            \"Clear vision and mission\",\n            \"Strong market opportunity\",\n            \"Confident delivery\"\n          ],\n          improvements: [\n            \"Provide more specific metrics\",\n            \"Address potential risks\",\n            \"Strengthen competitive moat explanation\"\n          ]\n        });\n        setIsLoading(false);\n      }, 2000);\n    }\n  }, [router]);\n\n  const shareResults = () => {\n    const shareText = `Just practiced my VC pitch with Pitchpal! 🚀\\n\\nOverall Score: ${results?.scores.overall}/100\\n• Confidence: ${results?.scores.confidence}/100\\n• Clarity: ${results?.scores.clarity}/100\\n• Vision: ${results?.scores.vision}/100\\n\\nReady to pitch to real investors! 💪`;\n    \n    if (navigator.share) {\n      navigator.share({\n        title: 'My Pitchpal Results',\n        text: shareText,\n        url: window.location.origin\n      });\n    } else {\n      navigator.clipboard.writeText(shareText);\n      alert('Results copied to clipboard!');\n    }\n  };\n\n  const startNewPitch = () => {\n    sessionStorage.removeItem('pitchpal_user');\n    router.push('/');\n  };\n\n  if (!userData || isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n        <motion.div\n          animate={{ rotate: 360 }}\n          transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n          className=\"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full\"\n        />\n      </div>\n    );\n  }\n\n  if (!results) return null;\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4\">\n      <div className=\"max-w-4xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-8\"\n        >\n          <h1 className=\"text-4xl font-bold text-white mb-4\">Pitch Results</h1>\n          <p className=\"text-gray-300 text-lg\">\n            Great job, {userData.name}! Here's how your {userData.startupName} pitch performed.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n          {/* Scores */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20\"\n          >\n            <h2 className=\"text-2xl font-bold text-white mb-6\">Your Scores</h2>\n            \n            <div className=\"space-y-4\">\n              {Object.entries(results.scores).map(([key, value]) => (\n                <div key={key}>\n                  <div className=\"flex justify-between text-white mb-2\">\n                    <span className=\"capitalize\">{key}</span>\n                    <span className=\"font-semibold\">{value}/100</span>\n                  </div>\n                  <div className=\"w-full bg-gray-700 rounded-full h-3\">\n                    <motion.div\n                      initial={{ width: 0 }}\n                      animate={{ width: `${value}%` }}\n                      transition={{ duration: 1, delay: 0.5 }}\n                      className={`h-3 rounded-full ${\n                        value >= 80 ? 'bg-green-500' :\n                        value >= 60 ? 'bg-yellow-500' : 'bg-red-500'\n                      }`}\n                    />\n                  </div>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Summary */}\n          <motion.div\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20\"\n          >\n            <h2 className=\"text-2xl font-bold text-white mb-4\">Summary</h2>\n            <p className=\"text-gray-300 leading-relaxed\">{results.summary}</p>\n          </motion.div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n          {/* Strengths */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20\"\n          >\n            <h2 className=\"text-2xl font-bold text-white mb-4\">Strengths</h2>\n            <ul className=\"space-y-2\">\n              {results.strengths.map((strength, index) => (\n                <li key={index} className=\"text-green-400 flex items-start\">\n                  <span className=\"mr-2\">✓</span>\n                  <span>{strength}</span>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Areas for Improvement */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20\"\n          >\n            <h2 className=\"text-2xl font-bold text-white mb-4\">Areas for Improvement</h2>\n            <ul className=\"space-y-2\">\n              {results.improvements.map((improvement, index) => (\n                <li key={index} className=\"text-yellow-400 flex items-start\">\n                  <span className=\"mr-2\">→</span>\n                  <span>{improvement}</span>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n        </div>\n\n        {/* Action buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 1 }}\n          className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n        >\n          <button\n            onClick={shareResults}\n            className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-3 px-8 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200\"\n          >\n            Share Results\n          </button>\n          \n          <button\n            onClick={startNewPitch}\n            className=\"bg-white/10 border border-white/20 text-white font-semibold py-3 px-8 rounded-lg hover:bg-white/20 transition-all duration-200\"\n          >\n            Practice Again\n          </button>\n        </motion.div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AA2Be,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAuB;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,MAAM,aAAa,eAAe,OAAO,CAAC;QAC1C,IAAI,CAAC,YAAY;YACf,OAAO,IAAI,CAAC;YACZ;QACF;QACA,YAAY,KAAK,KAAK,CAAC;QAEvB,kCAAkC;QAClC,MAAM,gBAAgB,eAAe,OAAO,CAAC;QAC7C,IAAI,eAAe;YACjB,WAAW,KAAK,KAAK,CAAC;YACtB,aAAa;QACf,OAAO;YACL,gDAAgD;YAChD,WAAW;gBACT,MAAM,WAAW,KAAK,KAAK,CAAC;gBAC5B,WAAW;oBACT,YAAY;oBACZ,SAAS,GAAG,SAAS,IAAI,CAAC,WAAW,EAAE,SAAS,WAAW,CAAC,oJAAoJ,CAAC;oBACjN,QAAQ;wBACN,YAAY;wBACZ,SAAS;wBACT,QAAQ;wBACR,SAAS;oBACX;oBACA,UAAU;wBACR;wBACA;wBACA;qBACD;oBACD,WAAW;wBACT;wBACA;wBACA;qBACD;oBACD,cAAc;wBACZ;wBACA;wBACA;qBACD;gBACH;gBACA,aAAa;YACf,GAAG;QACL;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,eAAe;QACnB,MAAM,YAAY,CAAC,+DAA+D,EAAE,SAAS,OAAO,QAAQ,oBAAoB,EAAE,SAAS,OAAO,WAAW,iBAAiB,EAAE,SAAS,OAAO,QAAQ,gBAAgB,EAAE,SAAS,OAAO,OAAO,4CAA4C,CAAC;QAE9R,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBACd,OAAO;gBACP,MAAM;gBACN,KAAK,OAAO,QAAQ,CAAC,MAAM;YAC7B;QACF,OAAO;YACL,UAAU,SAAS,CAAC,SAAS,CAAC;YAC9B,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB;QACpB,eAAe,UAAU,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,YAAY,WAAW;QAC1B,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAS;gBAC5D,WAAU;;;;;;;;;;;IAIlB;IAEA,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6WAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6WAAC;4BAAE,WAAU;;gCAAwB;gCACvB,SAAS,IAAI;gCAAC;gCAAmB,SAAS,WAAW;gCAAC;;;;;;;;;;;;;8BAItE,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6WAAC;oCAAG,WAAU;8CAAqC;;;;;;8CAEnD,6WAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,QAAQ,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC/C,6WAAC;;8DACC,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAK,WAAU;sEAAc;;;;;;sEAC9B,6WAAC;4DAAK,WAAU;;gEAAiB;gEAAM;;;;;;;;;;;;;8DAEzC,6WAAC;oDAAI,WAAU;8DACb,cAAA,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS;4DAAE,OAAO,GAAG,MAAM,CAAC,CAAC;wDAAC;wDAC9B,YAAY;4DAAE,UAAU;4DAAG,OAAO;wDAAI;wDACtC,WAAW,CAAC,iBAAiB,EAC3B,SAAS,KAAK,iBACd,SAAS,KAAK,kBAAkB,cAChC;;;;;;;;;;;;2CAbE;;;;;;;;;;;;;;;;sCAsBhB,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6WAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6WAAC;oCAAE,WAAU;8CAAiC,QAAQ,OAAO;;;;;;;;;;;;;;;;;;8BAIjE,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6WAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6WAAC;oCAAG,WAAU;8CACX,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAChC,6WAAC;4CAAe,WAAU;;8DACxB,6WAAC;oDAAK,WAAU;8DAAO;;;;;;8DACvB,6WAAC;8DAAM;;;;;;;2CAFA;;;;;;;;;;;;;;;;sCASf,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6WAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6WAAC;oCAAG,WAAU;8CACX,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBACtC,6WAAC;4CAAe,WAAU;;8DACxB,6WAAC;oDAAK,WAAU;8DAAO;;;;;;8DACvB,6WAAC;8DAAM;;;;;;;2CAFA;;;;;;;;;;;;;;;;;;;;;;8BAUjB,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAE;oBACtC,WAAU;;sCAEV,6WAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAID,6WAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}]}