{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/app/api/voice/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\ninterface VoiceRequest {\n  text: string;\n  voice_id?: string;\n}\n\n// Voice ID from ElevenLabs documentation\nconst VOICE_ID = \"Xb7hH8MSUJpSbSDYk0k2\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { text, voice_id = VOICE_ID }: VoiceRequest = await request.json();\n\n    if (!text) {\n      return NextResponse.json(\n        { error: 'Text is required' },\n        { status: 400 }\n      );\n    }\n\n    if (!process.env.ELEVENLABS_API_KEY) {\n      throw new Error('ElevenLabs API key not configured');\n    }\n\n    // Return WebSocket configuration for streaming\n    const wsUrl = `wss://api.elevenlabs.io/v1/text-to-speech/${voice_id}/stream-input?model_id=eleven_flash_v2_5`;\n\n    return NextResponse.json({\n      success: true,\n      websocket_url: wsUrl,\n      api_key: process.env.ELEVENLABS_API_KEY,\n      voice_settings: {\n        stability: 0.5,\n        similarity_boost: 0.8,\n        use_speaker_boost: false\n      },\n      generation_config: {\n        chunk_length_schedule: [120, 160, 250, 290]\n      },\n      type: 'websocket_streaming'\n    });\n\n  } catch (error) {\n    console.error('Voice API error:', error);\n    return NextResponse.json(\n      { error: `Failed to get WebSocket config: ${error instanceof Error ? error.message : 'Unknown error'}` },\n      { status: 500 }\n    );\n  }\n}\n\n\n"], "names": [], "mappings": ";;;AAAA;;AAOA,yCAAyC;AACzC,MAAM,WAAW;AAEV,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,WAAW,QAAQ,EAAE,GAAiB,MAAM,QAAQ,IAAI;QAEtE,IAAI,CAAC,MAAM;YACT,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YACnC,MAAM,IAAI,MAAM;QAClB;QAEA,+CAA+C;QAC/C,MAAM,QAAQ,CAAC,0CAA0C,EAAE,SAAS,wCAAwC,CAAC;QAE7G,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,eAAe;YACf,SAAS,QAAQ,GAAG,CAAC,kBAAkB;YACvC,gBAAgB;gBACd,WAAW;gBACX,kBAAkB;gBAClB,mBAAmB;YACrB;YACA,mBAAmB;gBACjB,uBAAuB;oBAAC;oBAAK;oBAAK;oBAAK;iBAAI;YAC7C;YACA,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,CAAC,gCAAgC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAAC,GACvG;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}