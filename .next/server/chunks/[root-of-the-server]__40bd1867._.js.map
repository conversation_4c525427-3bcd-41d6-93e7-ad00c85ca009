{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/app/api/voice/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\ninterface VoiceRequest {\n  text: string;\n  voice_id?: string;\n}\n\n// <PERSON> voice ID from ElevenLabs\nconst CHRIS_VOICE_ID = \"iP95p4xoKVk53GoZ742B\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { text, voice_id = CHRIS_VOICE_ID }: VoiceRequest = await request.json();\n\n    if (!text) {\n      return NextResponse.json(\n        { error: 'Text is required' },\n        { status: 400 }\n      );\n    }\n\n    if (!process.env.ELEVENLABS_API_KEY) {\n      throw new Error('ElevenLabs API key not configured');\n    }\n\n    const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voice_id}`, {\n      method: 'POST',\n      headers: {\n        'Accept': 'audio/mpeg',\n        'Content-Type': 'application/json',\n        'xi-api-key': process.env.ELEVENLABS_API_KEY,\n      },\n      body: JSON.stringify({\n        text,\n        model_id: \"eleven_flash_v2_5\",\n        voice_settings: {\n          stability: 0.5,\n          similarity_boost: 0.5,\n          style: 0.0,\n          use_speaker_boost: true\n        }\n      }),\n    });\n\n    if (!response.ok) {\n      const errorText = await response.text();\n      throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText} - ${errorText}`);\n    }\n\n    const audioBuffer = await response.arrayBuffer();\n\n    return new NextResponse(audioBuffer, {\n      headers: {\n        'Content-Type': 'audio/mpeg',\n        'Content-Length': audioBuffer.byteLength.toString(),\n        'Cache-Control': 'public, max-age=31536000',\n      },\n    });\n\n  } catch (error) {\n    console.error('Voice API error:', error);\n    return NextResponse.json(\n      { error: `Failed to synthesize voice: ${error instanceof Error ? error.message : 'Unknown error'}` },\n      { status: 500 }\n    );\n  }\n}\n\n\n"], "names": [], "mappings": ";;;AAAA;;AAOA,iCAAiC;AACjC,MAAM,iBAAiB;AAEhB,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,WAAW,cAAc,EAAE,GAAiB,MAAM,QAAQ,IAAI;QAE5E,IAAI,CAAC,MAAM;YACT,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YACnC,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,CAAC,4CAA4C,EAAE,UAAU,EAAE;YACtF,QAAQ;YACR,SAAS;gBACP,UAAU;gBACV,gBAAgB;gBAChB,cAAc,QAAQ,GAAG,CAAC,kBAAkB;YAC9C;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA,UAAU;gBACV,gBAAgB;oBACd,WAAW;oBACX,kBAAkB;oBAClB,OAAO;oBACP,mBAAmB;gBACrB;YACF;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;QAClG;QAEA,MAAM,cAAc,MAAM,SAAS,WAAW;QAE9C,OAAO,IAAI,+PAAA,CAAA,eAAY,CAAC,aAAa;YACnC,SAAS;gBACP,gBAAgB;gBAChB,kBAAkB,YAAY,UAAU,CAAC,QAAQ;gBACjD,iBAAiB;YACnB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,CAAC,4BAA4B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAAC,GACnG;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}