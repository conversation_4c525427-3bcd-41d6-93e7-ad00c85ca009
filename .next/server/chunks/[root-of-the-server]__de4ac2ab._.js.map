{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/app/api/voice-ws/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport WebSocket from 'ws';\n\n// Voice ID from ElevenLabs documentation\nconst VOICE_ID = \"Xb7hH8MSUJpSbSDYk0k2\";\n\nexport async function GET(request: NextRequest) {\n  const { searchParams } = new URL(request.url);\n  const text = searchParams.get('text');\n  \n  if (!text) {\n    return new Response('Text parameter required', { status: 400 });\n  }\n\n  if (!process.env.ELEVENLABS_API_KEY) {\n    return new Response('ElevenLabs API key not configured', { status: 500 });\n  }\n\n  // Create a readable stream for Server-Sent Events\n  const stream = new ReadableStream({\n    start(controller) {\n      // Connect to ElevenLabs WebSocket with proper headers\n      const uri = `wss://api.elevenlabs.io/v1/text-to-speech/${VOICE_ID}/stream-input?model_id=eleven_flash_v2_5`;\n      const websocket = new WebSocket(uri, {\n        headers: { 'xi-api-key': process.env.ELEVENLABS_API_KEY },\n      });\n\n      websocket.on('open', () => {\n        console.log('Connected to ElevenLabs WebSocket');\n        \n        // Send initial configuration - exactly like the docs\n        websocket.send(JSON.stringify({\n          text: ' ',\n          voice_settings: {\n            stability: 0.5,\n            similarity_boost: 0.8,\n            use_speaker_boost: false,\n          },\n          generation_config: { chunk_length_schedule: [120, 160, 250, 290] },\n        }));\n\n        // Send the actual text\n        websocket.send(JSON.stringify({ text: text }));\n        \n        // Send empty string to indicate the end of the text sequence\n        websocket.send(JSON.stringify({ text: '' }));\n      });\n\n      websocket.on('message', (data) => {\n        try {\n          const message = JSON.parse(data.toString());\n          \n          // Send the message to the client via Server-Sent Events\n          controller.enqueue(\n            new TextEncoder().encode(`data: ${JSON.stringify(message)}\\n\\n`)\n          );\n          \n          // If this is the last message or an error, close the stream\n          if (message.isFinal || message.error) {\n            controller.close();\n            websocket.close();\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n          controller.error(error);\n        }\n      });\n\n      websocket.on('close', () => {\n        console.log('ElevenLabs WebSocket closed');\n        controller.close();\n      });\n\n      websocket.on('error', (error) => {\n        console.error('ElevenLabs WebSocket error:', error);\n        controller.error(error);\n      });\n    },\n  });\n\n  return new Response(stream, {\n    headers: {\n      'Content-Type': 'text/event-stream',\n      'Cache-Control': 'no-cache',\n      'Connection': 'keep-alive',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;AACA;AAAA;;AAEA,yCAAyC;AACzC,MAAM,WAAW;AAEV,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,OAAO,aAAa,GAAG,CAAC;IAE9B,IAAI,CAAC,MAAM;QACT,OAAO,IAAI,SAAS,2BAA2B;YAAE,QAAQ;QAAI;IAC/D;IAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB,EAAE;QACnC,OAAO,IAAI,SAAS,qCAAqC;YAAE,QAAQ;QAAI;IACzE;IAEA,kDAAkD;IAClD,MAAM,SAAS,IAAI,eAAe;QAChC,OAAM,UAAU;YACd,sDAAsD;YACtD,MAAM,MAAM,CAAC,0CAA0C,EAAE,SAAS,wCAAwC,CAAC;YAC3G,MAAM,YAAY,IAAI,gMAAA,CAAA,UAAS,CAAC,KAAK;gBACnC,SAAS;oBAAE,cAAc,QAAQ,GAAG,CAAC,kBAAkB;gBAAC;YAC1D;YAEA,UAAU,EAAE,CAAC,QAAQ;gBACnB,QAAQ,GAAG,CAAC;gBAEZ,qDAAqD;gBACrD,UAAU,IAAI,CAAC,KAAK,SAAS,CAAC;oBAC5B,MAAM;oBACN,gBAAgB;wBACd,WAAW;wBACX,kBAAkB;wBAClB,mBAAmB;oBACrB;oBACA,mBAAmB;wBAAE,uBAAuB;4BAAC;4BAAK;4BAAK;4BAAK;yBAAI;oBAAC;gBACnE;gBAEA,uBAAuB;gBACvB,UAAU,IAAI,CAAC,KAAK,SAAS,CAAC;oBAAE,MAAM;gBAAK;gBAE3C,6DAA6D;gBAC7D,UAAU,IAAI,CAAC,KAAK,SAAS,CAAC;oBAAE,MAAM;gBAAG;YAC3C;YAEA,UAAU,EAAE,CAAC,WAAW,CAAC;gBACvB,IAAI;oBACF,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,QAAQ;oBAExC,wDAAwD;oBACxD,WAAW,OAAO,CAChB,IAAI,cAAc,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC,SAAS,IAAI,CAAC;oBAGjE,4DAA4D;oBAC5D,IAAI,QAAQ,OAAO,IAAI,QAAQ,KAAK,EAAE;wBACpC,WAAW,KAAK;wBAChB,UAAU,KAAK;oBACjB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;oBAClD,WAAW,KAAK,CAAC;gBACnB;YACF;YAEA,UAAU,EAAE,CAAC,SAAS;gBACpB,QAAQ,GAAG,CAAC;gBACZ,WAAW,KAAK;YAClB;YAEA,UAAU,EAAE,CAAC,SAAS,CAAC;gBACrB,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,WAAW,KAAK,CAAC;YACnB;QACF;IACF;IAEA,OAAO,IAAI,SAAS,QAAQ;QAC1B,SAAS;YACP,gBAAgB;YAChB,iBAAiB;YACjB,cAAc;QAChB;IACF;AACF", "debugId": null}}]}