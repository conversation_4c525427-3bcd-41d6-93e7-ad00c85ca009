{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/components/VideoCall.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface VideoCallProps {\n  isActive: boolean;\n  isListening: boolean;\n  isAISpeaking: boolean;\n  currentQuestion: string;\n  onStartCall: () => void;\n  onEndCall: () => void;\n}\n\nexport default function VideoCall({\n  isActive,\n  isListening,\n  isAISpeaking,\n  currentQuestion,\n  onStartCall,\n  onEndCall\n}: VideoCallProps) {\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const [stream, setStream] = useState<MediaStream | null>(null);\n  const [hasPermission, setHasPermission] = useState<boolean | null>(null);\n\n  useEffect(() => {\n    initializeCamera();\n    return () => {\n      if (stream) {\n        stream.getTracks().forEach(track => track.stop());\n      }\n    };\n  }, []);\n\n  const initializeCamera = async () => {\n    try {\n      const mediaStream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          width: { ideal: 1280 },\n          height: { ideal: 720 },\n          facingMode: 'user'\n        },\n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true\n        }\n      });\n      \n      setStream(mediaStream);\n      setHasPermission(true);\n      \n      if (videoRef.current) {\n        videoRef.current.srcObject = mediaStream;\n      }\n    } catch (error) {\n      console.error('Error accessing camera:', error);\n      setHasPermission(false);\n    }\n  };\n\n  if (hasPermission === false) {\n    return (\n      <div className=\"min-h-screen bg-black flex items-center justify-center\">\n        <div className=\"text-center text-white\">\n          <div className=\"text-6xl mb-4\">📹</div>\n          <h2 className=\"text-2xl font-bold mb-4\">Camera Access Required</h2>\n          <p className=\"text-gray-400 mb-6\">\n            Please allow camera and microphone access to start your pitch practice.\n          </p>\n          <button\n            onClick={initializeCamera}\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg\"\n          >\n            Grant Access\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-black flex flex-col\">\n      {/* Main video area */}\n      <div className=\"flex-1 flex\">\n        {/* User video */}\n        <div className=\"flex-1 relative\">\n          <video\n            ref={videoRef}\n            autoPlay\n            muted\n            playsInline\n            className=\"w-full h-full object-cover\"\n          />\n          \n          {/* User label */}\n          <div className=\"absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1 rounded-lg backdrop-blur-sm\">\n            You\n          </div>\n          \n          {/* Listening indicator */}\n          <AnimatePresence>\n            {isListening && (\n              <motion.div\n                initial={{ scale: 0.8, opacity: 0 }}\n                animate={{ scale: 1, opacity: 1 }}\n                exit={{ scale: 0.8, opacity: 0 }}\n                className=\"absolute top-4 left-4 bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-2\"\n              >\n                <motion.div\n                  animate={{ scale: [1, 1.2, 1] }}\n                  transition={{ repeat: Infinity, duration: 1 }}\n                  className=\"w-2 h-2 bg-white rounded-full\"\n                />\n                <span>Listening...</span>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n\n        {/* AI/VC video area */}\n        <div className=\"flex-1 relative bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center\">\n          <div className=\"text-center text-white\">\n            {/* AI Avatar */}\n            <motion.div\n              animate={isAISpeaking ? { scale: [1, 1.05, 1] } : {}}\n              transition={{ repeat: isAISpeaking ? Infinity : 0, duration: 2 }}\n              className=\"w-40 h-40 bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center mb-6 mx-auto shadow-2xl\"\n            >\n              <span className=\"text-6xl font-bold\">K</span>\n            </motion.div>\n            \n            <h3 className=\"text-2xl font-semibold mb-2\">Kenard (CEO)</h3>\n            <p className=\"text-gray-400 text-sm mb-4\">Venture Capital Partner</p>\n            \n            {/* Speaking indicator */}\n            <AnimatePresence>\n              {isAISpeaking && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: 10 }}\n                  className=\"flex items-center justify-center space-x-1\"\n                >\n                  <div className=\"text-green-400 text-sm font-medium\">Speaking</div>\n                  <motion.div\n                    animate={{ scale: [1, 1.2, 1] }}\n                    transition={{ repeat: Infinity, duration: 0.8 }}\n                    className=\"w-2 h-2 bg-green-400 rounded-full\"\n                  />\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n          \n          {/* VC label */}\n          <div className=\"absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded-lg backdrop-blur-sm\">\n            Kenard (CEO)\n          </div>\n        </div>\n      </div>\n\n      {/* Question display */}\n      <AnimatePresence>\n        {currentQuestion && isActive && (\n          <motion.div\n            initial={{ y: 100, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            exit={{ y: 100, opacity: 0 }}\n            className=\"bg-gray-900/95 backdrop-blur-sm p-6 border-t border-gray-700\"\n          >\n            <div className=\"max-w-4xl mx-auto text-center\">\n              <p className=\"text-white text-lg font-medium\">{currentQuestion}</p>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Start call overlay */}\n      <AnimatePresence>\n        {!isActive && hasPermission && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center\"\n          >\n            <div className=\"text-center\">\n              <motion.div\n                initial={{ scale: 0.9, opacity: 0 }}\n                animate={{ scale: 1, opacity: 1 }}\n                transition={{ delay: 0.2 }}\n                className=\"mb-8\"\n              >\n                <h2 className=\"text-3xl font-bold text-white mb-4\">\n                  Ready to Start Your Pitch?\n                </h2>\n                <p className=\"text-gray-300 text-lg\">\n                  You'll be interviewed by Kenard, an experienced VC partner\n                </p>\n              </motion.div>\n              \n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={onStartCall}\n                className=\"bg-green-600 hover:bg-green-700 text-white text-xl font-semibold px-12 py-4 rounded-xl shadow-lg transition-colors\"\n              >\n                Start Call\n              </motion.button>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAce,SAAS,UAAU,EAChC,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,eAAe,EACf,WAAW,EACX,SAAS,EACM;;IACf,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAsB;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAkB;IAEnE,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;+BAAE;YACR;YACA;uCAAO;oBACL,IAAI,QAAQ;wBACV,OAAO,SAAS,GAAG,OAAO;mDAAC,CAAA,QAAS,MAAM,IAAI;;oBAChD;gBACF;;QACF;8BAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,cAAc,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAC5D,OAAO;oBACL,OAAO;wBAAE,OAAO;oBAAK;oBACrB,QAAQ;wBAAE,OAAO;oBAAI;oBACrB,YAAY;gBACd;gBACA,OAAO;oBACL,kBAAkB;oBAClB,kBAAkB;oBAClB,iBAAiB;gBACnB;YACF;YAEA,UAAU;YACV,iBAAiB;YAEjB,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,iBAAiB;QACnB;IACF;IAEA,IAAI,kBAAkB,OAAO;QAC3B,qBACE,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,4TAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,4TAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,4TAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCACC,KAAK;gCACL,QAAQ;gCACR,KAAK;gCACL,WAAW;gCACX,WAAU;;;;;;0CAIZ,4TAAC;gCAAI,WAAU;0CAAwF;;;;;;0CAKvG,4TAAC,wUAAA,CAAA,kBAAe;0CACb,6BACC,4TAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;wCAAK,SAAS;oCAAE;oCAClC,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCAChC,MAAM;wCAAE,OAAO;wCAAK,SAAS;oCAAE;oCAC/B,WAAU;;sDAEV,4TAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,QAAQ;gDAAU,UAAU;4CAAE;4CAC5C,WAAU;;;;;;sDAEZ,4TAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAOd,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;kDAEb,4TAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS,eAAe;4CAAE,OAAO;gDAAC;gDAAG;gDAAM;6CAAE;wCAAC,IAAI,CAAC;wCACnD,YAAY;4CAAE,QAAQ,eAAe,WAAW;4CAAG,UAAU;wCAAE;wCAC/D,WAAU;kDAEV,cAAA,4TAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;kDAGvC,4TAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,4TAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,4TAAC,wUAAA,CAAA,kBAAe;kDACb,8BACC,4TAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC1B,WAAU;;8DAEV,4TAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,4TAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,OAAO;4DAAC;4DAAG;4DAAK;yDAAE;oDAAC;oDAC9B,YAAY;wDAAE,QAAQ;wDAAU,UAAU;oDAAI;oDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAQpB,4TAAC;gCAAI,WAAU;0CAAyF;;;;;;;;;;;;;;;;;;0BAO5G,4TAAC,wUAAA,CAAA,kBAAe;0BACb,mBAAmB,0BAClB,4TAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;wBAAK,SAAS;oBAAE;oBAC9B,SAAS;wBAAE,GAAG;wBAAG,SAAS;oBAAE;oBAC5B,MAAM;wBAAE,GAAG;wBAAK,SAAS;oBAAE;oBAC3B,WAAU;8BAEV,cAAA,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;;;;;0BAOvD,4TAAC,wUAAA,CAAA,kBAAe;0BACb,CAAC,YAAY,+BACZ,4TAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;8BAEV,cAAA,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;oCAAK,SAAS;gCAAE;gCAClC,SAAS;oCAAE,OAAO;oCAAG,SAAS;gCAAE;gCAChC,YAAY;oCAAE,OAAO;gCAAI;gCACzB,WAAU;;kDAEV,4TAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAGnD,4TAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,4TAAC,yUAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA3MwB;KAAA", "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/components/AudioHandler.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState, useCallback } from 'react';\n\ninterface AudioHandlerProps {\n  isListening: boolean;\n  onSpeechResult: (transcript: string) => void;\n  onSpeechEnd: () => void;\n  onError: (error: string) => void;\n}\n\ndeclare global {\n  interface Window {\n    SpeechRecognition: any;\n    webkitSpeechRecognition: any;\n  }\n}\n\nexport default function AudioHandler({\n  isListening,\n  onSpeechResult,\n  onSpeechEnd,\n  onError\n}: AudioHandlerProps) {\n  const recognitionRef = useRef<any>(null);\n  const [isSupported, setIsSupported] = useState(false);\n  const [currentTranscript, setCurrentTranscript] = useState('');\n\n  useEffect(() => {\n    // Check if speech recognition is supported\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    \n    if (SpeechRecognition) {\n      setIsSupported(true);\n      \n      // Initialize speech recognition\n      const recognition = new SpeechRecognition();\n      recognition.continuous = true;\n      recognition.interimResults = true;\n      recognition.lang = 'en-US';\n      recognition.maxAlternatives = 1;\n\n      recognition.onstart = () => {\n        console.log('Speech recognition started');\n      };\n\n      recognition.onresult = (event: any) => {\n        let finalTranscript = '';\n        let interimTranscript = '';\n\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript;\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript;\n          } else {\n            interimTranscript += transcript;\n          }\n        }\n\n        const fullTranscript = finalTranscript || interimTranscript;\n        setCurrentTranscript(fullTranscript);\n\n        if (finalTranscript) {\n          onSpeechResult(finalTranscript.trim());\n        }\n      };\n\n      recognition.onerror = (event: any) => {\n        console.error('Speech recognition error:', event.error);\n        onError(`Speech recognition error: ${event.error}`);\n      };\n\n      recognition.onend = () => {\n        console.log('Speech recognition ended');\n        onSpeechEnd();\n        setCurrentTranscript('');\n      };\n\n      recognitionRef.current = recognition;\n    } else {\n      setIsSupported(false);\n      onError('Speech recognition is not supported in this browser');\n    }\n\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop();\n      }\n    };\n  }, [onSpeechResult, onSpeechEnd, onError]);\n\n  useEffect(() => {\n    if (!isSupported || !recognitionRef.current) return;\n\n    if (isListening) {\n      try {\n        recognitionRef.current.start();\n      } catch (error) {\n        console.error('Error starting speech recognition:', error);\n      }\n    } else {\n      try {\n        recognitionRef.current.stop();\n      } catch (error) {\n        console.error('Error stopping speech recognition:', error);\n      }\n    }\n  }, [isListening, isSupported]);\n\n  const speakText = useCallback(async (text: string): Promise<void> => {\n    return new Promise((resolve, reject) => {\n      if (!('speechSynthesis' in window)) {\n        reject(new Error('Speech synthesis not supported'));\n        return;\n      }\n\n      // Cancel any ongoing speech\n      window.speechSynthesis.cancel();\n\n      const utterance = new SpeechSynthesisUtterance(text);\n      \n      // Configure voice settings\n      utterance.rate = 0.9;\n      utterance.pitch = 1.0;\n      utterance.volume = 1.0;\n\n      // Try to use a professional-sounding voice\n      const voices = window.speechSynthesis.getVoices();\n      const preferredVoice = voices.find(voice => \n        voice.name.includes('Alex') || \n        voice.name.includes('Daniel') || \n        voice.name.includes('Google US English')\n      );\n      \n      if (preferredVoice) {\n        utterance.voice = preferredVoice;\n      }\n\n      utterance.onend = () => {\n        resolve();\n      };\n\n      utterance.onerror = (event) => {\n        reject(new Error(`Speech synthesis error: ${event.error}`));\n      };\n\n      window.speechSynthesis.speak(utterance);\n    });\n  }, []);\n\n  // Expose speakText function to parent components\n  useEffect(() => {\n    (window as any).pitchpalSpeakText = speakText;\n    return () => {\n      delete (window as any).pitchpalSpeakText;\n    };\n  }, [speakText]);\n\n  if (!isSupported) {\n    return (\n      <div className=\"fixed bottom-4 right-4 bg-red-600 text-white p-4 rounded-lg shadow-lg\">\n        <p className=\"text-sm\">\n          Speech recognition is not supported in this browser. \n          Please use Chrome, Edge, or Safari for the best experience.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      {/* Live transcript display */}\n      {currentTranscript && isListening && (\n        <div className=\"fixed bottom-20 left-4 right-4 bg-black/80 text-white p-4 rounded-lg backdrop-blur-sm\">\n          <div className=\"text-sm text-gray-400 mb-1\">You're saying:</div>\n          <div className=\"text-white\">{currentTranscript}</div>\n        </div>\n      )}\n    </>\n  );\n}\n\n// Utility function to synthesize speech using ElevenLabs WebSocket streaming\nexport async function synthesizeWithElevenLabs(text: string): Promise<void> {\n  console.log('Starting ElevenLabs synthesis for text:', text);\n\n  try {\n    // Get WebSocket configuration from API\n    console.log('Fetching WebSocket config from /api/voice...');\n    const response = await fetch('/api/voice', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ text }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`Failed to get WebSocket config: ${response.statusText}`);\n    }\n\n    const config = await response.json();\n    console.log('WebSocket config received:', config);\n\n    // Use WebSocket streaming for real-time audio\n    await streamAudioWithWebSocket(text, config);\n\n  } catch (error) {\n    console.error('ElevenLabs synthesis error:', error);\n    console.log('Falling back to browser speech synthesis...');\n\n    // Fallback to browser speech synthesis\n    if ('speechSynthesis' in window) {\n      return new Promise((resolve, reject) => {\n        const utterance = new SpeechSynthesisUtterance(text);\n        utterance.rate = 0.9;\n        utterance.pitch = 1.0;\n        utterance.volume = 1.0;\n        utterance.onend = () => resolve();\n        utterance.onerror = () => reject(new Error('Browser synthesis failed'));\n        window.speechSynthesis.speak(utterance);\n      });\n    }\n    throw error;\n  }\n}\n\n// WebSocket streaming function based on ElevenLabs documentation\nasync function streamAudioWithWebSocket(text: string, config: any): Promise<void> {\n  return new Promise((resolve, reject) => {\n    // Add API key to WebSocket URL since headers aren't supported in browser WebSocket\n    const wsUrl = `${config.websocket_url}&xi_api_key=${config.api_key}`;\n    const websocket = new WebSocket(wsUrl);\n\n    let audioChunks: string[] = [];\n    let hasStartedPlaying = false;\n\n    websocket.onopen = () => {\n      console.log('ElevenLabs WebSocket connected');\n\n      // Send initial configuration with voice settings\n      websocket.send(JSON.stringify({\n        text: ' ',\n        voice_settings: config.voice_settings,\n        generation_config: config.generation_config\n      }));\n\n      // Send the actual text\n      websocket.send(JSON.stringify({ text: text }));\n\n      // Send empty string to close the connection\n      websocket.send(JSON.stringify({ text: '' }));\n    };\n\n    websocket.onmessage = async (event) => {\n      try {\n        const data = JSON.parse(event.data);\n        console.log('WebSocket message received:', data);\n        console.log('Message keys:', Object.keys(data));\n        console.log('Full message content:', JSON.stringify(data, null, 2));\n        console.log('Has audio property:', !!data.audio);\n\n        if (data.audio) {\n          console.log('Audio chunk received, length:', data.audio.length);\n          audioChunks.push(data.audio);\n\n          // Start playing immediately when we get the first chunk\n          if (!hasStartedPlaying) {\n            hasStartedPlaying = true;\n            console.log('Starting audio playback with', audioChunks.length, 'chunks');\n            playAudioChunks(audioChunks);\n          }\n        } else {\n          console.log('No audio property in message');\n          // Check if there's an error in the response\n          if (data.error) {\n            console.error('ElevenLabs WebSocket error:', data.error);\n          }\n        }\n      } catch (error) {\n        console.error('Error processing WebSocket message:', error);\n      }\n    };\n\n    websocket.onclose = async () => {\n      console.log('ElevenLabs WebSocket closed');\n\n      // Make sure all audio is played\n      if (audioChunks.length > 0 && !hasStartedPlaying) {\n        await playAudioChunks(audioChunks);\n      }\n\n      resolve();\n    };\n\n    websocket.onerror = (error) => {\n      console.error('WebSocket error:', error);\n      reject(new Error('WebSocket connection failed'));\n    };\n\n    // Timeout after 30 seconds\n    setTimeout(() => {\n      if (websocket.readyState === WebSocket.OPEN) {\n        websocket.close();\n        reject(new Error('WebSocket timeout'));\n      }\n    }, 30000);\n  });\n}\n\n// Helper function to play audio chunks as MP3\nasync function playAudioChunks(base64Chunks: string[]): Promise<void> {\n  try {\n    // Combine all base64 chunks into one\n    const combinedBase64 = base64Chunks.join('');\n\n    // Convert base64 to blob\n    const binaryString = atob(combinedBase64);\n    const bytes = new Uint8Array(binaryString.length);\n    for (let i = 0; i < binaryString.length; i++) {\n      bytes[i] = binaryString.charCodeAt(i);\n    }\n\n    const audioBlob = new Blob([bytes], { type: 'audio/mpeg' });\n    const audioUrl = URL.createObjectURL(audioBlob);\n\n    // Play the audio\n    const audio = new Audio(audioUrl);\n\n    return new Promise((resolve, reject) => {\n      audio.onended = () => {\n        URL.revokeObjectURL(audioUrl);\n        resolve();\n      };\n\n      audio.onerror = () => {\n        URL.revokeObjectURL(audioUrl);\n        reject(new Error('Audio playback failed'));\n      };\n\n      audio.play().catch(reject);\n    });\n\n  } catch (error) {\n    console.error('Error playing audio chunks:', error);\n    throw error;\n  }\n}\n\n\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAkBe,SAAS,aAAa,EACnC,WAAW,EACX,cAAc,EACd,WAAW,EACX,OAAO,EACW;;IAClB,MAAM,iBAAiB,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAO;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;kCAAE;YACR,2CAA2C;YAC3C,MAAM,oBAAoB,OAAO,iBAAiB,IAAI,OAAO,uBAAuB;YAEpF,IAAI,mBAAmB;gBACrB,eAAe;gBAEf,gCAAgC;gBAChC,MAAM,cAAc,IAAI;gBACxB,YAAY,UAAU,GAAG;gBACzB,YAAY,cAAc,GAAG;gBAC7B,YAAY,IAAI,GAAG;gBACnB,YAAY,eAAe,GAAG;gBAE9B,YAAY,OAAO;8CAAG;wBACpB,QAAQ,GAAG,CAAC;oBACd;;gBAEA,YAAY,QAAQ;8CAAG,CAAC;wBACtB,IAAI,kBAAkB;wBACtB,IAAI,oBAAoB;wBAExB,IAAK,IAAI,IAAI,MAAM,WAAW,EAAE,IAAI,MAAM,OAAO,CAAC,MAAM,EAAE,IAAK;4BAC7D,MAAM,aAAa,MAAM,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU;4BACjD,IAAI,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE;gCAC5B,mBAAmB;4BACrB,OAAO;gCACL,qBAAqB;4BACvB;wBACF;wBAEA,MAAM,iBAAiB,mBAAmB;wBAC1C,qBAAqB;wBAErB,IAAI,iBAAiB;4BACnB,eAAe,gBAAgB,IAAI;wBACrC;oBACF;;gBAEA,YAAY,OAAO;8CAAG,CAAC;wBACrB,QAAQ,KAAK,CAAC,6BAA6B,MAAM,KAAK;wBACtD,QAAQ,CAAC,0BAA0B,EAAE,MAAM,KAAK,EAAE;oBACpD;;gBAEA,YAAY,KAAK;8CAAG;wBAClB,QAAQ,GAAG,CAAC;wBACZ;wBACA,qBAAqB;oBACvB;;gBAEA,eAAe,OAAO,GAAG;YAC3B,OAAO;gBACL,eAAe;gBACf,QAAQ;YACV;YAEA;0CAAO;oBACL,IAAI,eAAe,OAAO,EAAE;wBAC1B,eAAe,OAAO,CAAC,IAAI;oBAC7B;gBACF;;QACF;iCAAG;QAAC;QAAgB;QAAa;KAAQ;IAEzC,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,eAAe,CAAC,eAAe,OAAO,EAAE;YAE7C,IAAI,aAAa;gBACf,IAAI;oBACF,eAAe,OAAO,CAAC,KAAK;gBAC9B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,sCAAsC;gBACtD;YACF,OAAO;gBACL,IAAI;oBACF,eAAe,OAAO,CAAC,IAAI;gBAC7B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,sCAAsC;gBACtD;YACF;QACF;iCAAG;QAAC;QAAa;KAAY;IAE7B,MAAM,YAAY,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;+CAAE,OAAO;YACnC,OAAO,IAAI;uDAAQ,CAAC,SAAS;oBAC3B,IAAI,CAAC,CAAC,qBAAqB,MAAM,GAAG;wBAClC,OAAO,IAAI,MAAM;wBACjB;oBACF;oBAEA,4BAA4B;oBAC5B,OAAO,eAAe,CAAC,MAAM;oBAE7B,MAAM,YAAY,IAAI,yBAAyB;oBAE/C,2BAA2B;oBAC3B,UAAU,IAAI,GAAG;oBACjB,UAAU,KAAK,GAAG;oBAClB,UAAU,MAAM,GAAG;oBAEnB,2CAA2C;oBAC3C,MAAM,SAAS,OAAO,eAAe,CAAC,SAAS;oBAC/C,MAAM,iBAAiB,OAAO,IAAI;8EAAC,CAAA,QACjC,MAAM,IAAI,CAAC,QAAQ,CAAC,WACpB,MAAM,IAAI,CAAC,QAAQ,CAAC,aACpB,MAAM,IAAI,CAAC,QAAQ,CAAC;;oBAGtB,IAAI,gBAAgB;wBAClB,UAAU,KAAK,GAAG;oBACpB;oBAEA,UAAU,KAAK;+DAAG;4BAChB;wBACF;;oBAEA,UAAU,OAAO;+DAAG,CAAC;4BACnB,OAAO,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM,KAAK,EAAE;wBAC3D;;oBAEA,OAAO,eAAe,CAAC,KAAK,CAAC;gBAC/B;;QACF;8CAAG,EAAE;IAEL,iDAAiD;IACjD,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;kCAAE;YACP,OAAe,iBAAiB,GAAG;YACpC;0CAAO;oBACL,OAAO,AAAC,OAAe,iBAAiB;gBAC1C;;QACF;iCAAG;QAAC;KAAU;IAEd,IAAI,CAAC,aAAa;QAChB,qBACE,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAE,WAAU;0BAAU;;;;;;;;;;;IAM7B;IAEA,qBACE;kBAEG,qBAAqB,6BACpB,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;8BAA6B;;;;;;8BAC5C,4TAAC;oBAAI,WAAU;8BAAc;;;;;;;;;;;;;AAKvC;GAlKwB;KAAA;AAqKjB,eAAe,yBAAyB,IAAY;IACzD,QAAQ,GAAG,CAAC,2CAA2C;IAEvD,IAAI;QACF,uCAAuC;QACvC,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,MAAM,cAAc;YACzC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAK;QAC9B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,UAAU,EAAE;QAC1E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,8BAA8B;QAE1C,8CAA8C;QAC9C,MAAM,yBAAyB,MAAM;IAEvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,QAAQ,GAAG,CAAC;QAEZ,uCAAuC;QACvC,IAAI,qBAAqB,QAAQ;YAC/B,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,MAAM,YAAY,IAAI,yBAAyB;gBAC/C,UAAU,IAAI,GAAG;gBACjB,UAAU,KAAK,GAAG;gBAClB,UAAU,MAAM,GAAG;gBACnB,UAAU,KAAK,GAAG,IAAM;gBACxB,UAAU,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;gBAC3C,OAAO,eAAe,CAAC,KAAK,CAAC;YAC/B;QACF;QACA,MAAM;IACR;AACF;AAEA,iEAAiE;AACjE,eAAe,yBAAyB,IAAY,EAAE,MAAW;IAC/D,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,mFAAmF;QACnF,MAAM,QAAQ,GAAG,OAAO,aAAa,CAAC,YAAY,EAAE,OAAO,OAAO,EAAE;QACpE,MAAM,YAAY,IAAI,UAAU;QAEhC,IAAI,cAAwB,EAAE;QAC9B,IAAI,oBAAoB;QAExB,UAAU,MAAM,GAAG;YACjB,QAAQ,GAAG,CAAC;YAEZ,iDAAiD;YACjD,UAAU,IAAI,CAAC,KAAK,SAAS,CAAC;gBAC5B,MAAM;gBACN,gBAAgB,OAAO,cAAc;gBACrC,mBAAmB,OAAO,iBAAiB;YAC7C;YAEA,uBAAuB;YACvB,UAAU,IAAI,CAAC,KAAK,SAAS,CAAC;gBAAE,MAAM;YAAK;YAE3C,4CAA4C;YAC5C,UAAU,IAAI,CAAC,KAAK,SAAS,CAAC;gBAAE,MAAM;YAAG;QAC3C;QAEA,UAAU,SAAS,GAAG,OAAO;YAC3B,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;gBAClC,QAAQ,GAAG,CAAC,+BAA+B;gBAC3C,QAAQ,GAAG,CAAC,iBAAiB,OAAO,IAAI,CAAC;gBACzC,QAAQ,GAAG,CAAC,yBAAyB,KAAK,SAAS,CAAC,MAAM,MAAM;gBAChE,QAAQ,GAAG,CAAC,uBAAuB,CAAC,CAAC,KAAK,KAAK;gBAE/C,IAAI,KAAK,KAAK,EAAE;oBACd,QAAQ,GAAG,CAAC,iCAAiC,KAAK,KAAK,CAAC,MAAM;oBAC9D,YAAY,IAAI,CAAC,KAAK,KAAK;oBAE3B,wDAAwD;oBACxD,IAAI,CAAC,mBAAmB;wBACtB,oBAAoB;wBACpB,QAAQ,GAAG,CAAC,gCAAgC,YAAY,MAAM,EAAE;wBAChE,gBAAgB;oBAClB;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,4CAA4C;oBAC5C,IAAI,KAAK,KAAK,EAAE;wBACd,QAAQ,KAAK,CAAC,+BAA+B,KAAK,KAAK;oBACzD;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACF;QAEA,UAAU,OAAO,GAAG;YAClB,QAAQ,GAAG,CAAC;YAEZ,gCAAgC;YAChC,IAAI,YAAY,MAAM,GAAG,KAAK,CAAC,mBAAmB;gBAChD,MAAM,gBAAgB;YACxB;YAEA;QACF;QAEA,UAAU,OAAO,GAAG,CAAC;YACnB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO,IAAI,MAAM;QACnB;QAEA,2BAA2B;QAC3B,WAAW;YACT,IAAI,UAAU,UAAU,KAAK,UAAU,IAAI,EAAE;gBAC3C,UAAU,KAAK;gBACf,OAAO,IAAI,MAAM;YACnB;QACF,GAAG;IACL;AACF;AAEA,8CAA8C;AAC9C,eAAe,gBAAgB,YAAsB;IACnD,IAAI;QACF,qCAAqC;QACrC,MAAM,iBAAiB,aAAa,IAAI,CAAC;QAEzC,yBAAyB;QACzB,MAAM,eAAe,KAAK;QAC1B,MAAM,QAAQ,IAAI,WAAW,aAAa,MAAM;QAChD,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,KAAK,CAAC,EAAE,GAAG,aAAa,UAAU,CAAC;QACrC;QAEA,MAAM,YAAY,IAAI,KAAK;YAAC;SAAM,EAAE;YAAE,MAAM;QAAa;QACzD,MAAM,WAAW,IAAI,eAAe,CAAC;QAErC,iBAAiB;QACjB,MAAM,QAAQ,IAAI,MAAM;QAExB,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,OAAO,GAAG;gBACd,IAAI,eAAe,CAAC;gBACpB;YACF;YAEA,MAAM,OAAO,GAAG;gBACd,IAAI,eAAe,CAAC;gBACpB,OAAO,IAAI,MAAM;YACnB;YAEA,MAAM,IAAI,GAAG,KAAK,CAAC;QACrB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 831, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/types/index.ts"], "sourcesContent": ["export interface UserData {\n  name: string;\n  startupName: string;\n}\n\nexport interface ChatMessage {\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n  timestamp?: number;\n}\n\nexport interface CallState {\n  isActive: boolean;\n  currentLevel: number;\n  isListening: boolean;\n  isAISpeaking: boolean;\n  transcript: TranscriptEntry[];\n  currentQuestion: string;\n  sessionId?: string;\n}\n\nexport interface TranscriptEntry {\n  speaker: 'user' | 'ai';\n  message: string;\n  timestamp: number;\n}\n\nexport interface EscalationLevel {\n  level: number;\n  title: string;\n  questions: string[];\n  description?: string;\n}\n\nexport interface PitchScore {\n  confidence: number;\n  clarity: number;\n  vision: number;\n  overall: number;\n}\n\nexport interface PitchResults {\n  transcript: string;\n  summary: string;\n  scores: PitchScore;\n  feedback: string[];\n  strengths: string[];\n  improvements: string[];\n  duration?: number;\n  sessionId?: string;\n}\n\nexport interface VoiceSettings {\n  stability: number;\n  similarity_boost: number;\n  style: number;\n  use_speaker_boost: boolean;\n}\n\nexport interface ElevenLabsRequest {\n  text: string;\n  model_id: string;\n  voice_settings: VoiceSettings;\n}\n\nexport interface SessionData {\n  userName: string;\n  startupName: string;\n  transcript: TranscriptEntry[];\n  duration: number;\n  startTime: number;\n  endTime: number;\n}\n\nexport interface APIResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nexport interface ChatResponse {\n  message: string;\n  nextLevel: number;\n  shouldEscalate: boolean;\n}\n\nexport interface VoiceResponse {\n  success: boolean;\n  audioUrl?: string;\n  duration?: number;\n  message?: string;\n}\n\n// Constants\nexport const ESCALATION_LEVELS: EscalationLevel[] = [\n  {\n    level: 1,\n    title: \"Icebreakers\",\n    description: \"Getting to know your startup and the problem you're solving\",\n    questions: [\n      \"Tell me what you're building.\",\n      \"What's the problem you solve?\",\n      \"How did you come up with this idea?\"\n    ]\n  },\n  {\n    level: 2,\n    title: \"Market\",\n    description: \"Understanding your market and target audience\",\n    questions: [\n      \"How big is this market really?\",\n      \"Who's your target user?\",\n      \"How do you know people want this?\",\n      \"What's your go-to-market strategy?\"\n    ]\n  },\n  {\n    level: 3,\n    title: \"Product\",\n    description: \"Diving into your product and technology\",\n    questions: [\n      \"Why now?\",\n      \"What makes your tech defensible?\",\n      \"What's your unfair advantage?\",\n      \"How does your product work?\"\n    ]\n  },\n  {\n    level: 4,\n    title: \"Business\",\n    description: \"Exploring your business model and strategy\",\n    questions: [\n      \"What's your moat?\",\n      \"What if a major competitor enters the space?\",\n      \"How do you plan to make money?\",\n      \"What are your unit economics?\"\n    ]\n  },\n  {\n    level: 5,\n    title: \"Gut Punch\",\n    description: \"Challenging questions to test your resilience\",\n    questions: [\n      \"This sounds like a feature, not a company. Convince me otherwise.\",\n      \"Why won't this be commoditized in 2 years?\",\n      \"What if Google builds this tomorrow?\",\n      \"Why should I invest in you versus the 100 other startups I see?\"\n    ]\n  }\n];\n\nexport const VOICE_SETTINGS: VoiceSettings = {\n  stability: 0.5,\n  similarity_boost: 0.5,\n  style: 0.0,\n  use_speaker_boost: true\n};\n\nexport const SCORE_THRESHOLDS = {\n  EXCELLENT: 90,\n  GOOD: 75,\n  AVERAGE: 60,\n  NEEDS_IMPROVEMENT: 40\n} as const;\n\nexport type ScoreLevel = keyof typeof SCORE_THRESHOLDS;\n"], "names": [], "mappings": ";;;;;AA+FO,MAAM,oBAAuC;IAClD;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,WAAW;YACT;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,WAAW;YACT;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,WAAW;YACT;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,WAAW;YACT;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,WAAW;YACT;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,MAAM,iBAAgC;IAC3C,WAAW;IACX,kBAAkB;IAClB,OAAO;IACP,mBAAmB;AACrB;AAEO,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,SAAS;IACT,mBAAmB;AACrB", "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\nimport { PitchScore, SCORE_THRESHOLDS, ScoreLevel } from \"@/types\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDuration(seconds: number): string {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n}\n\nexport function getScoreLevel(score: number): ScoreLevel {\n  if (score >= SCORE_THRESHOLDS.EXCELLENT) return 'EXCELLENT';\n  if (score >= SCORE_THRESHOLDS.GOOD) return 'GOOD';\n  if (score >= SCORE_THRESHOLDS.AVERAGE) return 'AVERAGE';\n  return 'NEEDS_IMPROVEMENT';\n}\n\nexport function getScoreColor(score: number): string {\n  const level = getScoreLevel(score);\n  switch (level) {\n    case 'EXCELLENT':\n      return 'text-green-500';\n    case 'GOOD':\n      return 'text-blue-500';\n    case 'AVERAGE':\n      return 'text-yellow-500';\n    case 'NEEDS_IMPROVEMENT':\n      return 'text-red-500';\n    default:\n      return 'text-gray-500';\n  }\n}\n\nexport function getScoreGradient(score: number): string {\n  const level = getScoreLevel(score);\n  switch (level) {\n    case 'EXCELLENT':\n      return 'from-green-500 to-emerald-600';\n    case 'GOOD':\n      return 'from-blue-500 to-cyan-600';\n    case 'AVERAGE':\n      return 'from-yellow-500 to-orange-600';\n    case 'NEEDS_IMPROVEMENT':\n      return 'from-red-500 to-pink-600';\n    default:\n      return 'from-gray-500 to-gray-600';\n  }\n}\n\nexport function calculateOverallScore(scores: Omit<PitchScore, 'overall'>): number {\n  const { confidence, clarity, vision } = scores;\n  return Math.round((confidence + clarity + vision) / 3);\n}\n\nexport function generateSessionId(): string {\n  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n\nexport function formatTimestamp(timestamp: number): string {\n  return new Date(timestamp).toLocaleTimeString();\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function validateUserData(data: any): data is { name: string; startupName: string } {\n  return (\n    data &&\n    typeof data === 'object' &&\n    typeof data.name === 'string' &&\n    typeof data.startupName === 'string' &&\n    data.name.trim().length > 0 &&\n    data.startupName.trim().length > 0\n  );\n}\n\nexport function sanitizeInput(input: string): string {\n  return input.trim().replace(/[<>]/g, '');\n}\n\nexport function generateShareText(\n  userName: string,\n  startupName: string,\n  scores: PitchScore\n): string {\n  return `Just practiced my VC pitch for ${startupName} with Pitchpal! 🚀\n\nOverall Score: ${scores.overall}/100\n• Confidence: ${scores.confidence}/100\n• Clarity: ${scores.clarity}/100\n• Vision: ${scores.vision}/100\n\nReady to pitch to real investors! 💪\n\nTry Pitchpal: ${window.location.origin}`;\n}\n\nexport function copyToClipboard(text: string): Promise<boolean> {\n  if (navigator.clipboard && window.isSecureContext) {\n    return navigator.clipboard.writeText(text).then(() => true).catch(() => false);\n  } else {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    textArea.style.position = 'absolute';\n    textArea.style.left = '-999999px';\n    document.body.prepend(textArea);\n    textArea.select();\n    try {\n      document.execCommand('copy');\n      return Promise.resolve(true);\n    } catch (error) {\n      return Promise.resolve(false);\n    } finally {\n      textArea.remove();\n    }\n  }\n}\n\nexport function isWebRTCSupported(): boolean {\n  return !!(\n    navigator.mediaDevices &&\n    navigator.mediaDevices.getUserMedia &&\n    window.RTCPeerConnection\n  );\n}\n\nexport function isSpeechRecognitionSupported(): boolean {\n  return !!(\n    window.SpeechRecognition ||\n    (window as any).webkitSpeechRecognition\n  );\n}\n\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  if (typeof error === 'string') {\n    return error;\n  }\n  return 'An unknown error occurred';\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,UAAU;IACnC,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,cAAc,KAAa;IACzC,IAAI,SAAS,wHAAA,CAAA,mBAAgB,CAAC,SAAS,EAAE,OAAO;IAChD,IAAI,SAAS,wHAAA,CAAA,mBAAgB,CAAC,IAAI,EAAE,OAAO;IAC3C,IAAI,SAAS,wHAAA,CAAA,mBAAgB,CAAC,OAAO,EAAE,OAAO;IAC9C,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,QAAQ,cAAc;IAC5B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,iBAAiB,KAAa;IAC5C,MAAM,QAAQ,cAAc;IAC5B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,sBAAsB,MAAmC;IACvE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG;IACxC,OAAO,KAAK,KAAK,CAAC,CAAC,aAAa,UAAU,MAAM,IAAI;AACtD;AAEO,SAAS;IACd,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC3E;AAEO,SAAS,gBAAgB,SAAiB;IAC/C,OAAO,IAAI,KAAK,WAAW,kBAAkB;AAC/C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,iBAAiB,IAAS;IACxC,OACE,QACA,OAAO,SAAS,YAChB,OAAO,KAAK,IAAI,KAAK,YACrB,OAAO,KAAK,WAAW,KAAK,YAC5B,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,KAC1B,KAAK,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG;AAErC;AAEO,SAAS,cAAc,KAAa;IACzC,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS;AACvC;AAEO,SAAS,kBACd,QAAgB,EAChB,WAAmB,EACnB,MAAkB;IAElB,OAAO,CAAC,+BAA+B,EAAE,YAAY;;eAExC,EAAE,OAAO,OAAO,CAAC;cAClB,EAAE,OAAO,UAAU,CAAC;WACvB,EAAE,OAAO,OAAO,CAAC;UAClB,EAAE,OAAO,MAAM,CAAC;;;;cAIZ,EAAE,OAAO,QAAQ,CAAC,MAAM,EAAE;AACxC;AAEO,SAAS,gBAAgB,IAAY;IAC1C,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;QACjD,OAAO,UAAU,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,IAAM,MAAM,KAAK,CAAC,IAAM;IAC1E,OAAO;QACL,8BAA8B;QAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;QAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;QACtB,SAAS,IAAI,CAAC,OAAO,CAAC;QACtB,SAAS,MAAM;QACf,IAAI;YACF,SAAS,WAAW,CAAC;YACrB,OAAO,QAAQ,OAAO,CAAC;QACzB,EAAE,OAAO,OAAO;YACd,OAAO,QAAQ,OAAO,CAAC;QACzB,SAAU;YACR,SAAS,MAAM;QACjB;IACF;AACF;AAEO,SAAS;IACd,OAAO,CAAC,CAAC,CACP,UAAU,YAAY,IACtB,UAAU,YAAY,CAAC,YAAY,IACnC,OAAO,iBAAiB,AAC1B;AACF;AAEO,SAAS;IACd,OAAO,CAAC,CAAC,CACP,OAAO,iBAAiB,IACxB,AAAC,OAAe,uBAAuB,AACzC;AACF;AAEO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/app/call/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport VideoCall from '@/components/VideoCall';\nimport <PERSON>Handler, { synthesizeWithElevenLabs } from '@/components/AudioHandler';\nimport { UserData, CallState, TranscriptEntry, ESCALATION_LEVELS } from '@/types';\nimport { generateSessionId } from '@/lib/utils';\n\nexport default function CallPage() {\n  const [userData, setUserData] = useState<UserData | null>(null);\n  const [callState, setCallState] = useState<CallState>({\n    isActive: false,\n    currentLevel: 1,\n    isListening: false,\n    isAISpeaking: false,\n    transcript: [],\n    currentQuestion: \"\",\n    sessionId: generateSessionId()\n  });\n  const [error, setError] = useState<string>('');\n  const router = useRouter();\n\n  useEffect(() => {\n    // Get user data from sessionStorage\n    const storedData = sessionStorage.getItem('pitchpal_user');\n    if (!storedData) {\n      router.push('/');\n      return;\n    }\n    setUserData(JSON.parse(storedData));\n  }, [router]);\n\n  const startCall = async () => {\n    if (!userData) return;\n\n    setCallState(prev => ({ ...prev, isActive: true }));\n\n    // Start with first question\n    const firstQuestion = `Hello ${userData.name}, I'm excited to hear about ${userData.startupName}. ${ESCALATION_LEVELS[0].questions[0]}`;\n\n    setCallState(prev => ({\n      ...prev,\n      currentQuestion: firstQuestion,\n      isAISpeaking: true\n    }));\n\n    // Add AI message to transcript\n    const aiMessage: TranscriptEntry = {\n      speaker: 'ai',\n      message: firstQuestion,\n      timestamp: Date.now()\n    };\n\n    setCallState(prev => ({\n      ...prev,\n      transcript: [...prev.transcript, aiMessage]\n    }));\n\n    await speakQuestion(firstQuestion);\n  };\n\n  const speakQuestion = async (question: string) => {\n    try {\n      // Try ElevenLabs first, fallback to browser synthesis\n      await synthesizeWithElevenLabs(question);\n    } catch (error) {\n      console.error('Voice synthesis error:', error);\n      // Fallback to browser speech synthesis\n      if ('speechSynthesis' in window) {\n        const utterance = new SpeechSynthesisUtterance(question);\n        window.speechSynthesis.speak(utterance);\n      }\n    }\n\n    // After speaking, start listening\n    setTimeout(() => {\n      setCallState(prev => ({\n        ...prev,\n        isAISpeaking: false,\n        isListening: true\n      }));\n    }, 3000);\n  };\n\n  const handleSpeechResult = async (transcript: string) => {\n    if (!userData) return;\n\n    // Add user message to transcript\n    const userMessage: TranscriptEntry = {\n      speaker: 'user',\n      message: transcript,\n      timestamp: Date.now()\n    };\n\n    setCallState(prev => ({\n      ...prev,\n      transcript: [...prev.transcript, userMessage],\n      isListening: false\n    }));\n\n    // Get AI response\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          messages: [\n            { role: 'user', content: transcript }\n          ],\n          currentLevel: callState.currentLevel,\n          userName: userData.name,\n          startupName: userData.startupName\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.message) {\n        setCallState(prev => ({\n          ...prev,\n          currentQuestion: data.message,\n          isAISpeaking: true,\n          currentLevel: data.shouldEscalate ? data.nextLevel : prev.currentLevel\n        }));\n\n        // Add AI response to transcript\n        const aiResponse: TranscriptEntry = {\n          speaker: 'ai',\n          message: data.message,\n          timestamp: Date.now()\n        };\n\n        setCallState(prev => ({\n          ...prev,\n          transcript: [...prev.transcript, aiResponse]\n        }));\n\n        await speakQuestion(data.message);\n      }\n    } catch (error) {\n      console.error('Chat API error:', error);\n      setError('Failed to get AI response');\n    }\n  };\n\n  const handleSpeechEnd = () => {\n    // Speech recognition ended\n  };\n\n  const handleAudioError = (error: string) => {\n    setError(error);\n  };\n\n  const endCall = async () => {\n    if (!userData) return;\n\n    // Save session data\n    const sessionData = {\n      userName: userData.name,\n      startupName: userData.startupName,\n      transcript: callState.transcript,\n      duration: Math.floor((Date.now() - (callState.transcript[0]?.timestamp || Date.now())) / 1000),\n      startTime: callState.transcript[0]?.timestamp || Date.now(),\n      endTime: Date.now()\n    };\n\n    try {\n      const response = await fetch('/api/session', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(sessionData)\n      });\n\n      const results = await response.json();\n\n      // Store results for the results page\n      sessionStorage.setItem('pitchpal_results', JSON.stringify(results));\n\n    } catch (error) {\n      console.error('Session save error:', error);\n    }\n\n    setCallState(prev => ({ ...prev, isActive: false }));\n    router.push('/results');\n  };\n\n  if (!userData) {\n    return (\n      <div className=\"min-h-screen bg-black flex items-center justify-center\">\n        <div className=\"text-white text-xl\">Loading...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-black flex flex-col\">\n      {/* Header with call controls */}\n      <div className=\"flex justify-between items-center p-4 bg-gray-900/50 backdrop-blur-sm z-10\">\n        <div className=\"text-white\">\n          <h2 className=\"text-lg font-semibold\">{userData.name}</h2>\n          <p className=\"text-gray-400 text-sm\">{userData.startupName}</p>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          {callState.isActive && (\n            <div className=\"text-white text-sm\">\n              Level {callState.currentLevel}: {ESCALATION_LEVELS[callState.currentLevel - 1]?.title}\n            </div>\n          )}\n\n          <button\n            onClick={endCall}\n            className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors\"\n          >\n            End Call\n          </button>\n        </div>\n      </div>\n\n      {/* Error display */}\n      {error && (\n        <div className=\"bg-red-600 text-white p-3 text-center\">\n          {error}\n          <button\n            onClick={() => setError('')}\n            className=\"ml-4 underline\"\n          >\n            Dismiss\n          </button>\n        </div>\n      )}\n\n      {/* Video Call Component */}\n      <VideoCall\n        isActive={callState.isActive}\n        isListening={callState.isListening}\n        isAISpeaking={callState.isAISpeaking}\n        currentQuestion={callState.currentQuestion}\n        onStartCall={startCall}\n        onEndCall={endCall}\n      />\n\n      {/* Audio Handler Component */}\n      <AudioHandler\n        isListening={callState.isListening}\n        onSpeechResult={handleSpeechResult}\n        onSpeechEnd={handleSpeechEnd}\n        onError={handleAudioError}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,UAAU;QACV,cAAc;QACd,aAAa;QACb,cAAc;QACd,YAAY,EAAE;QACd,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;IAC7B;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;8BAAE;YACR,oCAAoC;YACpC,MAAM,aAAa,eAAe,OAAO,CAAC;YAC1C,IAAI,CAAC,YAAY;gBACf,OAAO,IAAI,CAAC;gBACZ;YACF;YACA,YAAY,KAAK,KAAK,CAAC;QACzB;6BAAG;QAAC;KAAO;IAEX,MAAM,YAAY;QAChB,IAAI,CAAC,UAAU;QAEf,aAAa,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAK,CAAC;QAEjD,4BAA4B;QAC5B,MAAM,gBAAgB,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,4BAA4B,EAAE,SAAS,WAAW,CAAC,EAAE,EAAE,wHAAA,CAAA,oBAAiB,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;QAEvI,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,iBAAiB;gBACjB,cAAc;YAChB,CAAC;QAED,+BAA+B;QAC/B,MAAM,YAA6B;YACjC,SAAS;YACT,SAAS;YACT,WAAW,KAAK,GAAG;QACrB;QAEA,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,YAAY;uBAAI,KAAK,UAAU;oBAAE;iBAAU;YAC7C,CAAC;QAED,MAAM,cAAc;IACtB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,sDAAsD;YACtD,MAAM,CAAA,GAAA,qIAAA,CAAA,2BAAwB,AAAD,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,uCAAuC;YACvC,IAAI,qBAAqB,QAAQ;gBAC/B,MAAM,YAAY,IAAI,yBAAyB;gBAC/C,OAAO,eAAe,CAAC,KAAK,CAAC;YAC/B;QACF;QAEA,kCAAkC;QAClC,WAAW;YACT,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,cAAc;oBACd,aAAa;gBACf,CAAC;QACH,GAAG;IACL;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,UAAU;QAEf,iCAAiC;QACjC,MAAM,cAA+B;YACnC,SAAS;YACT,SAAS;YACT,WAAW,KAAK,GAAG;QACrB;QAEA,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,YAAY;uBAAI,KAAK,UAAU;oBAAE;iBAAY;gBAC7C,aAAa;YACf,CAAC;QAED,kBAAkB;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU;wBACR;4BAAE,MAAM;4BAAQ,SAAS;wBAAW;qBACrC;oBACD,cAAc,UAAU,YAAY;oBACpC,UAAU,SAAS,IAAI;oBACvB,aAAa,SAAS,WAAW;gBACnC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,iBAAiB,KAAK,OAAO;wBAC7B,cAAc;wBACd,cAAc,KAAK,cAAc,GAAG,KAAK,SAAS,GAAG,KAAK,YAAY;oBACxE,CAAC;gBAED,gCAAgC;gBAChC,MAAM,aAA8B;oBAClC,SAAS;oBACT,SAAS,KAAK,OAAO;oBACrB,WAAW,KAAK,GAAG;gBACrB;gBAEA,aAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,YAAY;+BAAI,KAAK,UAAU;4BAAE;yBAAW;oBAC9C,CAAC;gBAED,MAAM,cAAc,KAAK,OAAO;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,SAAS;QACX;IACF;IAEA,MAAM,kBAAkB;IACtB,2BAA2B;IAC7B;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS;IACX;IAEA,MAAM,UAAU;QACd,IAAI,CAAC,UAAU;QAEf,oBAAoB;QACpB,MAAM,cAAc;YAClB,UAAU,SAAS,IAAI;YACvB,aAAa,SAAS,WAAW;YACjC,YAAY,UAAU,UAAU;YAChC,UAAU,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,UAAU,UAAU,CAAC,EAAE,EAAE,aAAa,KAAK,GAAG,EAAE,CAAC,IAAI;YACzF,WAAW,UAAU,UAAU,CAAC,EAAE,EAAE,aAAa,KAAK,GAAG;YACzD,SAAS,KAAK,GAAG;QACnB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YAEnC,qCAAqC;YACrC,eAAe,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;QAE5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC;QAEA,aAAa,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAM,CAAC;QAClD,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;0BAAqB;;;;;;;;;;;IAG1C;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAG,WAAU;0CAAyB,SAAS,IAAI;;;;;;0CACpD,4TAAC;gCAAE,WAAU;0CAAyB,SAAS,WAAW;;;;;;;;;;;;kCAG5D,4TAAC;wBAAI,WAAU;;4BACZ,UAAU,QAAQ,kBACjB,4TAAC;gCAAI,WAAU;;oCAAqB;oCAC3B,UAAU,YAAY;oCAAC;oCAAG,wHAAA,CAAA,oBAAiB,CAAC,UAAU,YAAY,GAAG,EAAE,EAAE;;;;;;;0CAIpF,4TAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;YAOJ,uBACC,4TAAC;gBAAI,WAAU;;oBACZ;kCACD,4TAAC;wBACC,SAAS,IAAM,SAAS;wBACxB,WAAU;kCACX;;;;;;;;;;;;0BAOL,4TAAC,kIAAA,CAAA,UAAS;gBACR,UAAU,UAAU,QAAQ;gBAC5B,aAAa,UAAU,WAAW;gBAClC,cAAc,UAAU,YAAY;gBACpC,iBAAiB,UAAU,eAAe;gBAC1C,aAAa;gBACb,WAAW;;;;;;0BAIb,4TAAC,qIAAA,CAAA,UAAY;gBACX,aAAa,UAAU,WAAW;gBAClC,gBAAgB;gBAChB,aAAa;gBACb,SAAS;;;;;;;;;;;;AAIjB;GAnPwB;;QAYP,oQAAA,CAAA,YAAS;;;KAZF", "debugId": null}}]}