{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/pitchpal/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\n\nexport default function Home() {\n  const [name, setName] = useState('');\n  const [startupName, setStartupName] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const router = useRouter();\n\n  const handleStartPitch = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!name.trim() || !startupName.trim()) return;\n\n    setIsLoading(true);\n\n    // Store user data in sessionStorage for the call\n    sessionStorage.setItem('pitchpal_user', JSON.stringify({\n      name: name.trim(),\n      startupName: startupName.trim()\n    }));\n\n    // Navigate to call page\n    router.push('/call');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4\">\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8 }}\n        className=\"max-w-md w-full\"\n      >\n        <div className=\"text-center mb-8\">\n          <motion.h1\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-4xl font-bold text-white mb-4\"\n          >\n            Pitchpal\n          </motion.h1>\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"text-gray-300 text-lg\"\n          >\n            Practice your VC pitch with AI-powered feedback\n          </motion.p>\n        </div>\n\n        <motion.form\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          onSubmit={handleStartPitch}\n          className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20\"\n        >\n          <div className=\"space-y-6\">\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-200 mb-2\">\n                Your Name\n              </label>\n              <input\n                type=\"text\"\n                id=\"name\"\n                value={name}\n                onChange={(e) => setName(e.target.value)}\n                className=\"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                placeholder=\"Enter your name\"\n                required\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"startup\" className=\"block text-sm font-medium text-gray-200 mb-2\">\n                Startup Name\n              </label>\n              <input\n                type=\"text\"\n                id=\"startup\"\n                value={startupName}\n                onChange={(e) => setStartupName(e.target.value)}\n                className=\"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                placeholder=\"Enter your startup name\"\n                required\n              />\n            </div>\n\n            <motion.button\n              type=\"submit\"\n              disabled={isLoading || !name.trim() || !startupName.trim()}\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              className=\"w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold py-3 px-6 rounded-lg hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n            >\n              {isLoading ? 'Starting...' : 'Start Your Pitch'}\n            </motion.button>\n          </div>\n        </motion.form>\n\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.8, delay: 1 }}\n          className=\"text-center mt-8 text-gray-400 text-sm\"\n        >\n          <p>Get ready for a realistic VC interview simulation</p>\n        </motion.div>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAChB,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI;QAEzC,aAAa;QAEb,iDAAiD;QACjD,eAAe,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YACrD,MAAM,KAAK,IAAI;YACf,aAAa,YAAY,IAAI;QAC/B;QAEA,wBAAwB;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAEV,4TAAC;oBAAI,WAAU;;sCACb,4TAAC,yUAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAGD,4TAAC,yUAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;;;;;;;8BAKH,4TAAC,yUAAA,CAAA,SAAM,CAAC,IAAI;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;oBACV,WAAU;8BAEV,cAAA,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;;kDACC,4TAAC;wCAAM,SAAQ;wCAAO,WAAU;kDAA+C;;;;;;kDAG/E,4TAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACvC,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAIZ,4TAAC;;kDACC,4TAAC;wCAAM,SAAQ;wCAAU,WAAU;kDAA+C;;;;;;kDAGlF,4TAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAIZ,4TAAC,yUAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,MAAK;gCACL,UAAU,aAAa,CAAC,KAAK,IAAI,MAAM,CAAC,YAAY,IAAI;gCACxD,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CAET,YAAY,gBAAgB;;;;;;;;;;;;;;;;;8BAKnC,4TAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAE;oBACtC,WAAU;8BAEV,cAAA,4TAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb;GA9GwB;;QAIP,oQAAA,CAAA,YAAS;;;KAJF", "debugId": null}}]}