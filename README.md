# Pitchpal 🚀

A web-based application that simulates venture capital (VC) interviews, allowing founders to practice their pitches with AI-powered feedback and real-time voice interaction.

## Features

- **Interactive VC Simulation**: Full-screen video call interface with AI-powered VC avatar
- **Real-time Voice Interaction**: Speech recognition for user input and text-to-speech for AI responses
- **Escalating Question Levels**: 5-level progression from icebreakers to challenging "gut punch" questions
- **Comprehensive Scoring**: AI analysis of confidence, clarity, and vision with detailed feedback
- **Social Sharing**: Share your pitch results on social media platforms
- **Modern UI**: Beautiful, responsive design with smooth animations using Framer Motion

## Tech Stack

### Frontend
- **Next.js 15** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **React Hooks** for state management

### Backend
- **Next.js API Routes** for serverless functions
- **GPT-4 Integration** (ready for OpenAI API)
- **ElevenLabs Voice Synthesis** (ready for integration)
- **Web Speech API** for speech recognition

### Key Technologies
- **WebRTC** for video capture
- **Speech Recognition API** for voice input
- **Speech Synthesis API** for voice output
- **Session Storage** for data persistence

## Getting Started

### Prerequisites
- Node.js 18+
- pnpm (recommended) or npm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd pitchpal
```

2. Install dependencies:
```bash
pnpm install
```

3. Run the development server:
```bash
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Environment Variables (Optional)

For production deployment, add these environment variables:

```env
OPENAI_API_KEY=your_openai_api_key
ELEVENLABS_API_KEY=your_elevenlabs_api_key
```

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   │   ├── chat/          # GPT-4 chat integration
│   │   ├── voice/         # ElevenLabs voice synthesis
│   │   └── session/       # Session analysis and scoring
│   ├── call/              # Video call interface
│   ├── results/           # Results and analytics page
│   └── page.tsx           # Landing page
├── components/            # Reusable React components
│   ├── VideoCall.tsx      # Main video call component
│   └── AudioHandler.tsx   # Speech recognition/synthesis
├── lib/                   # Utility functions
│   └── utils.ts           # Helper functions
└── types/                 # TypeScript type definitions
    └── index.ts           # Shared types
```

## Features in Detail

### 1. Landing Page
- User input form for name and startup name
- Beautiful gradient background with glass morphism effects
- Smooth animations and transitions

### 2. Video Call Interface
- Split-screen layout with user video and AI avatar
- Real-time status indicators (listening, speaking)
- Professional VC avatar with animated responses
- Question display at the bottom

### 3. Escalation System
- **Level 1 - Icebreakers**: Basic questions about the startup
- **Level 2 - Market**: Market size and target audience
- **Level 3 - Product**: Technology and defensibility
- **Level 4 - Business**: Business model and competition
- **Level 5 - Gut Punch**: Challenging fundamental questions

### 4. AI Analysis
- Real-time transcript generation
- Scoring on three key metrics:
  - **Confidence**: Delivery and presence
  - **Clarity**: Communication effectiveness
  - **Vision**: Strategic thinking and market understanding
- Detailed feedback with strengths and improvement areas

### 5. Results Dashboard
- Comprehensive score breakdown with visual progress bars
- Executive summary of the pitch performance
- Actionable feedback and recommendations
- Social sharing capabilities

## Browser Compatibility

- **Chrome**: Full support (recommended)
- **Safari**: Full support
- **Firefox**: Limited speech recognition support
- **Edge**: Full support

## Development

### Adding New Question Levels

Edit `src/types/index.ts` to add new escalation levels:

```typescript
export const ESCALATION_LEVELS: EscalationLevel[] = [
  // Add new levels here
];
```

### Customizing AI Responses

Modify the prompts in `src/app/api/chat/route.ts`:

```typescript
const ESCALATION_PROMPTS = {
  // Customize AI behavior for each level
};
```

### Integrating Real APIs

1. **OpenAI GPT-4**: Uncomment and configure the API call in `src/app/api/chat/route.ts`
2. **ElevenLabs**: Uncomment and configure the API call in `src/app/api/voice/route.ts`

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For questions or support, please open an issue on GitHub.

---

Built with ❤️ for founders who want to nail their VC pitch!
