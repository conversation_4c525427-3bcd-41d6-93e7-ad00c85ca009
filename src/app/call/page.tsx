'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import VideoCall from '@/components/VideoCall';
import <PERSON>Handler, { synthesizeWithElevenLabs } from '@/components/AudioHandler';
import { UserData, CallState, TranscriptEntry, ESCALATION_LEVELS } from '@/types';
import { generateSessionId } from '@/lib/utils';

export default function CallPage() {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [callState, setCallState] = useState<CallState>({
    isActive: false,
    currentLevel: 1,
    isListening: false,
    isAISpeaking: false,
    transcript: [],
    currentQuestion: "",
    sessionId: generateSessionId()
  });
  const [error, setError] = useState<string>('');
  const router = useRouter();

  useEffect(() => {
    // Get user data from sessionStorage
    const storedData = sessionStorage.getItem('pitchpal_user');
    if (!storedData) {
      router.push('/');
      return;
    }
    setUserData(JSON.parse(storedData));
  }, [router]);

  const startCall = async () => {
    if (!userData) return;

    setCallState(prev => ({ ...prev, isActive: true }));

    // Start with first question
    const firstQuestion = `Hello ${userData.name}, I'm excited to hear about ${userData.startupName}. ${ESCALATION_LEVELS[0].questions[0]}`;

    setCallState(prev => ({
      ...prev,
      currentQuestion: firstQuestion,
      isAISpeaking: true
    }));

    // Add AI message to transcript
    const aiMessage: TranscriptEntry = {
      speaker: 'ai',
      message: firstQuestion,
      timestamp: Date.now()
    };

    setCallState(prev => ({
      ...prev,
      transcript: [...prev.transcript, aiMessage]
    }));

    await speakQuestion(firstQuestion);
  };

  const speakQuestion = async (question: string) => {
    try {
      // Use ElevenLabs only - no fallback to crappy browser speech
      await synthesizeWithElevenLabs(question);
    } catch (error) {
      console.error('Voice synthesis error:', error);
      // Don't fallback to browser speech - just show error
      setError(`Voice synthesis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // After speaking, start listening
    setTimeout(() => {
      setCallState(prev => ({
        ...prev,
        isAISpeaking: false,
        isListening: true
      }));
    }, 3000);
  };

  const handleSpeechResult = async (transcript: string) => {
    if (!userData) return;

    // Add user message to transcript
    const userMessage: TranscriptEntry = {
      speaker: 'user',
      message: transcript,
      timestamp: Date.now()
    };

    setCallState(prev => ({
      ...prev,
      transcript: [...prev.transcript, userMessage],
      isListening: false
    }));

    // Get AI response
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [
            { role: 'user', content: transcript }
          ],
          currentLevel: callState.currentLevel,
          userName: userData.name,
          startupName: userData.startupName
        })
      });

      const data = await response.json();

      if (data.message) {
        setCallState(prev => ({
          ...prev,
          currentQuestion: data.message,
          isAISpeaking: true,
          currentLevel: data.shouldEscalate ? data.nextLevel : prev.currentLevel
        }));

        // Add AI response to transcript
        const aiResponse: TranscriptEntry = {
          speaker: 'ai',
          message: data.message,
          timestamp: Date.now()
        };

        setCallState(prev => ({
          ...prev,
          transcript: [...prev.transcript, aiResponse]
        }));

        await speakQuestion(data.message);
      }
    } catch (error) {
      console.error('Chat API error:', error);
      setError('Failed to get AI response');
    }
  };

  const handleSpeechEnd = () => {
    // Speech recognition ended
  };

  const handleAudioError = (error: string) => {
    setError(error);
  };

  const endCall = async () => {
    if (!userData) return;

    // Save session data
    const sessionData = {
      userName: userData.name,
      startupName: userData.startupName,
      transcript: callState.transcript,
      duration: Math.floor((Date.now() - (callState.transcript[0]?.timestamp || Date.now())) / 1000),
      startTime: callState.transcript[0]?.timestamp || Date.now(),
      endTime: Date.now()
    };

    try {
      const response = await fetch('/api/session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(sessionData)
      });

      const results = await response.json();

      // Store results for the results page
      sessionStorage.setItem('pitchpal_results', JSON.stringify(results));

    } catch (error) {
      console.error('Session save error:', error);
    }

    setCallState(prev => ({ ...prev, isActive: false }));
    router.push('/results');
  };

  if (!userData) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black flex flex-col">
      {/* Header with call controls */}
      <div className="flex justify-between items-center p-4 bg-gray-900/50 backdrop-blur-sm z-10">
        <div className="text-white">
          <h2 className="text-lg font-semibold">{userData.name}</h2>
          <p className="text-gray-400 text-sm">{userData.startupName}</p>
        </div>

        <div className="flex items-center space-x-4">
          {callState.isActive && (
            <div className="text-white text-sm">
              Level {callState.currentLevel}: {ESCALATION_LEVELS[callState.currentLevel - 1]?.title}
            </div>
          )}

          <button
            onClick={endCall}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            End Call
          </button>
        </div>
      </div>

      {/* Error display */}
      {error && (
        <div className="bg-red-600 text-white p-3 text-center">
          {error}
          <button
            onClick={() => setError('')}
            className="ml-4 underline"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Video Call Component */}
      <VideoCall
        isActive={callState.isActive}
        isListening={callState.isListening}
        isAISpeaking={callState.isAISpeaking}
        currentQuestion={callState.currentQuestion}
        onStartCall={startCall}
        onEndCall={endCall}
      />

      {/* Audio Handler Component */}
      <AudioHandler
        isListening={callState.isListening}
        onSpeechResult={handleSpeechResult}
        onSpeechEnd={handleSpeechEnd}
        onError={handleAudioError}
      />
    </div>
  );
}
