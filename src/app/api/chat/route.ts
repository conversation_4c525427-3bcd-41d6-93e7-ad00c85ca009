import { NextRequest, NextResponse } from 'next/server';

interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

interface ChatRequest {
  messages: ChatMessage[];
  currentLevel: number;
  userName: string;
  startupName: string;
}

const ESCALATION_PROMPTS = {
  1: "You are a venture capitalist conducting an initial interview. Ask icebreaker questions about the startup's core concept and problem they're solving. Be friendly but professional.",
  2: "You are a VC now diving into market questions. Ask about market size, target users, and market validation. Be more analytical.",
  3: "You are a VC focusing on product questions. Ask about timing, technology, and defensibility. Be more technical and probing.",
  4: "You are a VC asking business model questions. Focus on moats, competition, and business strategy. Be more challenging.",
  5: "You are a VC delivering gut punch questions. Challenge the fundamental premise of the business. Be tough but fair."
};

const LEVEL_QUESTIONS = {
  1: [
    "Tell me what you're building.",
    "What's the problem you solve?",
    "How did you come up with this idea?"
  ],
  2: [
    "How big is this market really?",
    "Who's your target user?",
    "How do you know people want this?"
  ],
  3: [
    "Why now?",
    "What makes your tech defensible?",
    "What's your unfair advantage?"
  ],
  4: [
    "What's your moat?",
    "What if a major competitor enters the space?",
    "How do you plan to make money?"
  ],
  5: [
    "This sounds like a feature, not a company. Convince me otherwise.",
    "Why won't this be commoditized in 2 years?",
    "What if Google builds this tomorrow?"
  ]
};

export async function POST(request: NextRequest) {
  try {
    const { messages, currentLevel, userName, startupName }: ChatRequest = await request.json();

    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OpenAI API key not configured');
    }

    const systemPrompt = ESCALATION_PROMPTS[currentLevel as keyof typeof ESCALATION_PROMPTS];
    const levelQuestions = LEVEL_QUESTIONS[currentLevel as keyof typeof LEVEL_QUESTIONS];

    // Build conversation context
    const conversationMessages = [
      {
        role: 'system' as const,
        content: `${systemPrompt}

You are interviewing ${userName} about their startup ${startupName}.

Guidelines:
- Ask one question at a time
- Keep responses under 50 words
- Be professional but conversational
- Build on their previous answers
- Use these level-appropriate questions as inspiration: ${levelQuestions.join(', ')}
- Escalate difficulty naturally based on their responses`
      },
      ...messages
    ];

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: conversationMessages,
        max_tokens: 100,
        temperature: 0.7,
        presence_penalty: 0.1,
        frequency_penalty: 0.1,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const aiResponse = data.choices[0].message.content;

    // Determine if we should escalate based on conversation length and level
    const shouldEscalate = messages.length >= 2 && currentLevel < 5 && Math.random() > 0.6;

    return NextResponse.json({
      message: aiResponse,
      nextLevel: currentLevel < 5 ? currentLevel + 1 : 5,
      shouldEscalate
    });

  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: `Failed to process chat request: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}
