import { NextRequest, NextResponse } from 'next/server';

interface VoiceRequest {
  text: string;
  voice_id?: string;
}

// Voice ID from ElevenLabs (using the one from documentation)
const VOICE_ID = "Xb7hH8MSUJpSbSDYk0k2";

export async function POST(request: NextRequest) {
  try {
    const { text, voice_id = VOICE_ID }: VoiceRequest = await request.json();

    if (!text) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }

    if (!process.env.ELEVENLABS_API_KEY) {
      throw new Error('ElevenLabs API key not configured');
    }

    // Return WebSocket configuration for streaming
    const wsUrl = `wss://api.elevenlabs.io/v1/text-to-speech/${voice_id}/stream-input?model_id=eleven_flash_v2_5`;

    return NextResponse.json({
      success: true,
      websocket_url: wsUrl,
      api_key: process.env.ELEVENLABS_API_KEY,
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.8,
        use_speaker_boost: false
      },
      generation_config: {
        chunk_length_schedule: [120, 160, 250, 290]
      },
      type: 'websocket_streaming'
    });

  } catch (error) {
    console.error('Voice API error:', error);
    return NextResponse.json(
      { error: `Failed to get WebSocket config: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}


