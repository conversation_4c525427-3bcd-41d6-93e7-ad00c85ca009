import { NextRequest, NextResponse } from 'next/server';

interface SessionData {
  userName: string;
  startupName: string;
  transcript: Array<{
    speaker: 'user' | 'ai';
    message: string;
    timestamp: number;
  }>;
  duration: number;
}

interface PitchScore {
  confidence: number;
  clarity: number;
  vision: number;
  overall: number;
}

interface SessionResults {
  transcript: string;
  summary: string;
  scores: PitchScore;
  feedback: string[];
  strengths: string[];
  improvements: string[];
}

export async function POST(request: NextRequest) {
  try {
    const sessionData: SessionData = await request.json();

    // Analyze the session and generate results
    const results = await analyzeSession(sessionData);

    return NextResponse.json(results);

  } catch (error) {
    console.error('Session API error:', error);
    return NextResponse.json(
      { error: 'Failed to process session' },
      { status: 500 }
    );
  }
}

async function analyzeSession(sessionData: SessionData): Promise<SessionResults> {
  const { userName, startupName, transcript, duration } = sessionData;

  // Generate transcript text
  const transcriptText = transcript
    .map(entry => `${entry.speaker === 'user' ? userName : 'VC'}: ${entry.message}`)
    .join('\n');

  if (!process.env.OPENAI_API_KEY) {
    throw new Error('OpenAI API key not configured');
  }

  try {
    // Use GPT-4o-mini to analyze the pitch
    const analysisPrompt = `You are an expert VC pitch analyst. Analyze this pitch transcript and provide detailed feedback.

Transcript:
${transcriptText}

Startup: ${startupName}
Founder: ${userName}
Duration: ${Math.floor(duration / 60)} minutes ${duration % 60} seconds

Please provide your analysis in the following JSON format:
{
  "confidence": <score 0-100>,
  "clarity": <score 0-100>,
  "vision": <score 0-100>,
  "summary": "<2-3 sentence executive summary>",
  "strengths": ["<strength 1>", "<strength 2>", "<strength 3>"],
  "improvements": ["<improvement 1>", "<improvement 2>", "<improvement 3>"],
  "feedback": ["<specific feedback 1>", "<specific feedback 2>", "<specific feedback 3>"]
}

Scoring criteria:
- Confidence: Delivery, presence, conviction, handling of questions
- Clarity: Communication effectiveness, structure, articulation
- Vision: Strategic thinking, market understanding, long-term perspective

Be specific and actionable in your feedback.`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an expert VC pitch analyst. Provide detailed, actionable feedback in valid JSON format only.'
          },
          {
            role: 'user',
            content: analysisPrompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.3,
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    const analysis = JSON.parse(data.choices[0].message.content);

    // Calculate overall score
    const overall = Math.round((analysis.confidence + analysis.clarity + analysis.vision) / 3);

    return {
      transcript: transcriptText,
      summary: analysis.summary,
      scores: {
        confidence: analysis.confidence,
        clarity: analysis.clarity,
        vision: analysis.vision,
        overall
      },
      feedback: analysis.feedback,
      strengths: analysis.strengths,
      improvements: analysis.improvements
    };

  } catch (error) {
    console.error('AI analysis error:', error);

    // Fallback to basic analysis if AI fails
    const wordCount = transcript
      .filter(entry => entry.speaker === 'user')
      .reduce((count, entry) => count + entry.message.split(' ').length, 0);

    const responseCount = transcript.filter(entry => entry.speaker === 'user').length;

    const confidence = Math.min(95, Math.max(60, 70 + (responseCount * 3)));
    const clarity = Math.min(95, Math.max(60, 65 + (wordCount / 10)));
    const vision = Math.min(95, Math.max(60, 75 + Math.random() * 20));
    const overall = Math.round((confidence + clarity + vision) / 3);

    return {
      transcript: transcriptText,
      summary: `${userName} presented ${startupName} in a ${duration > 300 ? 'comprehensive' : 'brief'} pitch session. Analysis shows ${overall >= 80 ? 'strong' : 'developing'} performance across key metrics.`,
      scores: { confidence, clarity, vision, overall },
      feedback: ["Good engagement with questions", "Clear communication style", "Room for improvement in specific areas"],
      strengths: ["Active participation", "Responsive to questions"],
      improvements: ["Provide more specific examples", "Strengthen key value propositions"]
    };
  }
}


