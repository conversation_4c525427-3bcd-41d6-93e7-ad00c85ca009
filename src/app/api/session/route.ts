import { NextRequest, NextResponse } from 'next/server';

interface SessionData {
  userName: string;
  startupName: string;
  transcript: Array<{
    speaker: 'user' | 'ai';
    message: string;
    timestamp: number;
  }>;
  duration: number;
}

interface PitchScore {
  confidence: number;
  clarity: number;
  vision: number;
  overall: number;
}

interface SessionResults {
  transcript: string;
  summary: string;
  scores: PitchScore;
  feedback: string[];
  strengths: string[];
  improvements: string[];
}

export async function POST(request: NextRequest) {
  try {
    const sessionData: SessionData = await request.json();

    // Analyze the session and generate results
    const results = await analyzeSession(sessionData);

    return NextResponse.json(results);

  } catch (error) {
    console.error('Session API error:', error);
    return NextResponse.json(
      { error: 'Failed to process session' },
      { status: 500 }
    );
  }
}

async function analyzeSession(sessionData: SessionData): Promise<SessionResults> {
  // In production, this would use GPT-4 to analyze the transcript
  // For demo, we'll generate mock results based on the session data
  
  const { userName, startupName, transcript, duration } = sessionData;
  
  // Generate transcript text
  const transcriptText = transcript
    .map(entry => `${entry.speaker === 'user' ? userName : 'VC'}: ${entry.message}`)
    .join('\n');

  // Mock scoring algorithm (in production, use AI analysis)
  const wordCount = transcript
    .filter(entry => entry.speaker === 'user')
    .reduce((count, entry) => count + entry.message.split(' ').length, 0);

  const responseCount = transcript.filter(entry => entry.speaker === 'user').length;
  
  // Calculate scores based on simple metrics
  const confidence = Math.min(95, Math.max(60, 70 + (responseCount * 3)));
  const clarity = Math.min(95, Math.max(60, 65 + (wordCount / 10)));
  const vision = Math.min(95, Math.max(60, 75 + Math.random() * 20));
  const overall = Math.round((confidence + clarity + vision) / 3);

  // Generate feedback based on performance
  const feedback = [];
  const strengths = [];
  const improvements = [];

  if (confidence >= 80) {
    strengths.push("Confident delivery and strong presence");
  } else {
    improvements.push("Work on building confidence in your delivery");
  }

  if (clarity >= 80) {
    strengths.push("Clear and articulate communication");
  } else {
    improvements.push("Focus on clearer articulation of key points");
  }

  if (vision >= 80) {
    strengths.push("Strong vision and market understanding");
  } else {
    improvements.push("Develop a clearer long-term vision");
  }

  // Add general feedback
  feedback.push("Good engagement with the interviewer");
  feedback.push("Demonstrated understanding of the market");
  
  if (duration < 300) { // Less than 5 minutes
    improvements.push("Consider providing more detailed responses");
  }

  // Generate summary using mock AI analysis
  const summary = `${userName} presented ${startupName} with ${overall >= 80 ? 'strong' : overall >= 60 ? 'good' : 'developing'} performance. The pitch demonstrated ${confidence >= 80 ? 'high' : 'moderate'} confidence levels and ${clarity >= 80 ? 'excellent' : 'good'} clarity in communication. ${vision >= 80 ? 'The vision was compelling and well-articulated.' : 'The vision could be strengthened with more specific details.'}`;

  return {
    transcript: transcriptText,
    summary,
    scores: {
      confidence,
      clarity,
      vision,
      overall
    },
    feedback,
    strengths,
    improvements
  };
}

// In production, you would also integrate with GPT-4 for more sophisticated analysis:
/*
async function analyzeWithGPT4(transcript: string, userName: string, startupName: string) {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: `You are an expert VC pitch analyst. Analyze the following pitch transcript and provide scores (0-100) for confidence, clarity, and vision. Also provide specific feedback, strengths, and areas for improvement.`
        },
        {
          role: 'user',
          content: `Analyze this pitch transcript for ${startupName} by ${userName}:\n\n${transcript}`
        }
      ],
      max_tokens: 1000,
      temperature: 0.3,
    }),
  });

  const data = await response.json();
  return data.choices[0].message.content;
}
*/
