import { NextRequest } from 'next/server';
import WebSocket from 'ws';

// Voice ID from ElevenLabs documentation
const VOICE_ID = "Xb7hH8MSUJpSbSDYk0k2";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const text = searchParams.get('text');
  
  if (!text) {
    return new Response('Text parameter required', { status: 400 });
  }

  if (!process.env.ELEVENLABS_API_KEY) {
    return new Response('ElevenLabs API key not configured', { status: 500 });
  }

  // Create a readable stream for Server-Sent Events
  const stream = new ReadableStream({
    start(controller) {
      // Connect to ElevenLabs WebSocket with proper headers
      const uri = `wss://api.elevenlabs.io/v1/text-to-speech/${VOICE_ID}/stream-input?model_id=eleven_flash_v2_5`;
      const websocket = new WebSocket(uri, {
        headers: { 'xi-api-key': process.env.ELEVENLABS_API_KEY },
      });

      websocket.on('open', () => {
        console.log('Connected to ElevenLabs WebSocket');
        
        // Send initial configuration - exactly like the docs
        websocket.send(JSON.stringify({
          text: ' ',
          voice_settings: {
            stability: 0.5,
            similarity_boost: 0.8,
            use_speaker_boost: false,
          },
          generation_config: { chunk_length_schedule: [120, 160, 250, 290] },
        }));

        // Send the actual text
        websocket.send(JSON.stringify({ text: text }));
        
        // Send empty string to indicate the end of the text sequence
        websocket.send(JSON.stringify({ text: '' }));
      });

      websocket.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          
          // Send the message to the client via Server-Sent Events
          controller.enqueue(
            new TextEncoder().encode(`data: ${JSON.stringify(message)}\n\n`)
          );
          
          // If this is the last message or an error, close the stream
          if (message.isFinal || message.error) {
            controller.close();
            websocket.close();
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
          controller.error(error);
        }
      });

      websocket.on('close', () => {
        console.log('ElevenLabs WebSocket closed');
        controller.close();
      });

      websocket.on('error', (error) => {
        console.error('ElevenLabs WebSocket error:', error);
        controller.error(error);
      });
    },
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  });
}
