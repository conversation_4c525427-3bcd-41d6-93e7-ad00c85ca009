'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';

interface UserData {
  name: string;
  startupName: string;
}

interface PitchScore {
  confidence: number;
  clarity: number;
  vision: number;
  overall: number;
}

interface PitchResults {
  transcript: string;
  summary: string;
  scores: PitchScore;
  feedback: string[];
  strengths: string[];
  improvements: string[];
}

export default function ResultsPage() {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [results, setResults] = useState<PitchResults | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Get user data from sessionStorage
    const storedData = sessionStorage.getItem('pitchpal_user');
    if (!storedData) {
      router.push('/');
      return;
    }
    setUserData(JSON.parse(storedData));

    // Get results from sessionStorage
    const storedResults = sessionStorage.getItem('pitchpal_results');
    if (storedResults) {
      setResults(JSON.parse(storedResults));
      setIsLoading(false);
    } else {
      // Fallback to mock results if no stored results
      setTimeout(() => {
        const userData = JSON.parse(storedData);
        setResults({
          transcript: "Sample transcript of the conversation...",
          summary: `${userData.name} presented ${userData.startupName}, demonstrating strong vision and market understanding. The pitch showed good confidence levels with clear articulation of the problem and solution.`,
          scores: {
            confidence: 85,
            clarity: 78,
            vision: 92,
            overall: 85
          },
          feedback: [
            "Strong opening with clear problem statement",
            "Good market size understanding",
            "Could improve on competitive analysis"
          ],
          strengths: [
            "Clear vision and mission",
            "Strong market opportunity",
            "Confident delivery"
          ],
          improvements: [
            "Provide more specific metrics",
            "Address potential risks",
            "Strengthen competitive moat explanation"
          ]
        });
        setIsLoading(false);
      }, 2000);
    }
  }, [router]);

  const shareResults = () => {
    const shareText = `Just practiced my VC pitch with Pitchpal! 🚀\n\nOverall Score: ${results?.scores.overall}/100\n• Confidence: ${results?.scores.confidence}/100\n• Clarity: ${results?.scores.clarity}/100\n• Vision: ${results?.scores.vision}/100\n\nReady to pitch to real investors! 💪`;
    
    if (navigator.share) {
      navigator.share({
        title: 'My Pitchpal Results',
        text: shareText,
        url: window.location.origin
      });
    } else {
      navigator.clipboard.writeText(shareText);
      alert('Results copied to clipboard!');
    }
  };

  const startNewPitch = () => {
    sessionStorage.removeItem('pitchpal_user');
    router.push('/');
  };

  if (!userData || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  if (!results) return null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold text-white mb-4">Pitch Results</h1>
          <p className="text-gray-300 text-lg">
            Great job, {userData.name}! Here's how your {userData.startupName} pitch performed.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Scores */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
          >
            <h2 className="text-2xl font-bold text-white mb-6">Your Scores</h2>
            
            <div className="space-y-4">
              {Object.entries(results.scores).map(([key, value]) => (
                <div key={key}>
                  <div className="flex justify-between text-white mb-2">
                    <span className="capitalize">{key}</span>
                    <span className="font-semibold">{value}/100</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-3">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${value}%` }}
                      transition={{ duration: 1, delay: 0.5 }}
                      className={`h-3 rounded-full ${
                        value >= 80 ? 'bg-green-500' :
                        value >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                    />
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Summary */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
          >
            <h2 className="text-2xl font-bold text-white mb-4">Summary</h2>
            <p className="text-gray-300 leading-relaxed">{results.summary}</p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Strengths */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
          >
            <h2 className="text-2xl font-bold text-white mb-4">Strengths</h2>
            <ul className="space-y-2">
              {results.strengths.map((strength, index) => (
                <li key={index} className="text-green-400 flex items-start">
                  <span className="mr-2">✓</span>
                  <span>{strength}</span>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Areas for Improvement */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
          >
            <h2 className="text-2xl font-bold text-white mb-4">Areas for Improvement</h2>
            <ul className="space-y-2">
              {results.improvements.map((improvement, index) => (
                <li key={index} className="text-yellow-400 flex items-start">
                  <span className="mr-2">→</span>
                  <span>{improvement}</span>
                </li>
              ))}
            </ul>
          </motion.div>
        </div>

        {/* Action buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="flex flex-col sm:flex-row gap-4 justify-center"
        >
          <button
            onClick={shareResults}
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-3 px-8 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
          >
            Share Results
          </button>
          
          <button
            onClick={startNewPitch}
            className="bg-white/10 border border-white/20 text-white font-semibold py-3 px-8 rounded-lg hover:bg-white/20 transition-all duration-200"
          >
            Practice Again
          </button>
        </motion.div>
      </div>
    </div>
  );
}
