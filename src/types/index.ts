export interface UserData {
  name: string;
  startupName: string;
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: number;
}

export interface CallState {
  isActive: boolean;
  currentLevel: number;
  isListening: boolean;
  isAISpeaking: boolean;
  transcript: TranscriptEntry[];
  currentQuestion: string;
  sessionId?: string;
}

export interface TranscriptEntry {
  speaker: 'user' | 'ai';
  message: string;
  timestamp: number;
}

export interface EscalationLevel {
  level: number;
  title: string;
  questions: string[];
  description?: string;
}

export interface PitchScore {
  confidence: number;
  clarity: number;
  vision: number;
  overall: number;
}

export interface PitchResults {
  transcript: string;
  summary: string;
  scores: PitchScore;
  feedback: string[];
  strengths: string[];
  improvements: string[];
  duration?: number;
  sessionId?: string;
}

export interface VoiceSettings {
  stability: number;
  similarity_boost: number;
  style: number;
  use_speaker_boost: boolean;
}

export interface ElevenLabsRequest {
  text: string;
  model_id: string;
  voice_settings: VoiceSettings;
}

export interface SessionData {
  userName: string;
  startupName: string;
  transcript: TranscriptEntry[];
  duration: number;
  startTime: number;
  endTime: number;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ChatResponse {
  message: string;
  nextLevel: number;
  shouldEscalate: boolean;
}

export interface VoiceResponse {
  success: boolean;
  audioUrl?: string;
  duration?: number;
  message?: string;
}

// Constants
export const ESCALATION_LEVELS: EscalationLevel[] = [
  {
    level: 1,
    title: "Icebreakers",
    description: "Getting to know your startup and the problem you're solving",
    questions: [
      "Tell me what you're building.",
      "What's the problem you solve?",
      "How did you come up with this idea?"
    ]
  },
  {
    level: 2,
    title: "Market",
    description: "Understanding your market and target audience",
    questions: [
      "How big is this market really?",
      "Who's your target user?",
      "How do you know people want this?",
      "What's your go-to-market strategy?"
    ]
  },
  {
    level: 3,
    title: "Product",
    description: "Diving into your product and technology",
    questions: [
      "Why now?",
      "What makes your tech defensible?",
      "What's your unfair advantage?",
      "How does your product work?"
    ]
  },
  {
    level: 4,
    title: "Business",
    description: "Exploring your business model and strategy",
    questions: [
      "What's your moat?",
      "What if a major competitor enters the space?",
      "How do you plan to make money?",
      "What are your unit economics?"
    ]
  },
  {
    level: 5,
    title: "Gut Punch",
    description: "Challenging questions to test your resilience",
    questions: [
      "This sounds like a feature, not a company. Convince me otherwise.",
      "Why won't this be commoditized in 2 years?",
      "What if Google builds this tomorrow?",
      "Why should I invest in you versus the 100 other startups I see?"
    ]
  }
];

export const VOICE_SETTINGS: VoiceSettings = {
  stability: 0.5,
  similarity_boost: 0.5,
  style: 0.0,
  use_speaker_boost: true
};

export const SCORE_THRESHOLDS = {
  EXCELLENT: 90,
  GOOD: 75,
  AVERAGE: 60,
  NEEDS_IMPROVEMENT: 40
} as const;

export type ScoreLevel = keyof typeof SCORE_THRESHOLDS;
