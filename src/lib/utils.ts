import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { PitchScore, SCORE_THRESHOLDS, ScoreLevel } from "@/types";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

export function getScoreLevel(score: number): ScoreLevel {
  if (score >= SCORE_THRESHOLDS.EXCELLENT) return 'EXCELLENT';
  if (score >= SCORE_THRESHOLDS.GOOD) return 'GOOD';
  if (score >= SCORE_THRESHOLDS.AVERAGE) return 'AVERAGE';
  return 'NEEDS_IMPROVEMENT';
}

export function getScoreColor(score: number): string {
  const level = getScoreLevel(score);
  switch (level) {
    case 'EXCELLENT':
      return 'text-green-500';
    case 'GOOD':
      return 'text-blue-500';
    case 'AVERAGE':
      return 'text-yellow-500';
    case 'NEEDS_IMPROVEMENT':
      return 'text-red-500';
    default:
      return 'text-gray-500';
  }
}

export function getScoreGradient(score: number): string {
  const level = getScoreLevel(score);
  switch (level) {
    case 'EXCELLENT':
      return 'from-green-500 to-emerald-600';
    case 'GOOD':
      return 'from-blue-500 to-cyan-600';
    case 'AVERAGE':
      return 'from-yellow-500 to-orange-600';
    case 'NEEDS_IMPROVEMENT':
      return 'from-red-500 to-pink-600';
    default:
      return 'from-gray-500 to-gray-600';
  }
}

export function calculateOverallScore(scores: Omit<PitchScore, 'overall'>): number {
  const { confidence, clarity, vision } = scores;
  return Math.round((confidence + clarity + vision) / 3);
}

export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function formatTimestamp(timestamp: number): string {
  return new Date(timestamp).toLocaleTimeString();
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

export function validateUserData(data: any): data is { name: string; startupName: string } {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.name === 'string' &&
    typeof data.startupName === 'string' &&
    data.name.trim().length > 0 &&
    data.startupName.trim().length > 0
  );
}

export function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

export function generateShareText(
  userName: string,
  startupName: string,
  scores: PitchScore
): string {
  return `Just practiced my VC pitch for ${startupName} with Pitchpal! 🚀

Overall Score: ${scores.overall}/100
• Confidence: ${scores.confidence}/100
• Clarity: ${scores.clarity}/100
• Vision: ${scores.vision}/100

Ready to pitch to real investors! 💪

Try Pitchpal: ${window.location.origin}`;
}

export function copyToClipboard(text: string): Promise<boolean> {
  if (navigator.clipboard && window.isSecureContext) {
    return navigator.clipboard.writeText(text).then(() => true).catch(() => false);
  } else {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'absolute';
    textArea.style.left = '-999999px';
    document.body.prepend(textArea);
    textArea.select();
    try {
      document.execCommand('copy');
      return Promise.resolve(true);
    } catch (error) {
      return Promise.resolve(false);
    } finally {
      textArea.remove();
    }
  }
}

export function isWebRTCSupported(): boolean {
  return !!(
    navigator.mediaDevices &&
    navigator.mediaDevices.getUserMedia &&
    window.RTCPeerConnection
  );
}

export function isSpeechRecognitionSupported(): boolean {
  return !!(
    window.SpeechRecognition ||
    (window as any).webkitSpeechRecognition
  );
}

export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
}

export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}
