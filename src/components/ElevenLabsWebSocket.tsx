'use client';

import { useRef, useCallback, useEffect } from 'react';

interface ElevenLabsWebSocketProps {
  onAudioStart?: () => void;
  onAudioEnd?: () => void;
  onError?: (error: string) => void;
}

interface WebSocketMessage {
  audio?: string; // base64 encoded audio
  isFinal?: boolean;
  normalizedAlignment?: any;
}

export default function ElevenLabsWebSocket({
  onAudioStart,
  onAudioEnd,
  onError
}: ElevenLabsWebSocketProps) {
  const wsRef = useRef<WebSocket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const audioQueueRef = useRef<AudioBuffer[]>([]);
  const isPlayingRef = useRef(false);

  // Chris voice ID from ElevenLabs
  const CHRIS_VOICE_ID = "iP95p4xoKVk53GoZ742B";

  const initializeAudioContext = useCallback(async () => {
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }
    
    if (audioContextRef.current.state === 'suspended') {
      await audioContextRef.current.resume();
    }
  }, []);

  const connectWebSocket = useCallback(async () => {
    if (!process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY) {
      onError?.('ElevenLabs API key not configured');
      return;
    }

    try {
      await initializeAudioContext();

      const wsUrl = `wss://api.elevenlabs.io/v1/text-to-speech/${CHRIS_VOICE_ID}/stream-input?model_id=eleven_flash_v2_5&output_format=pcm_16000&xi_api_key=${process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY}`;

      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('ElevenLabs WebSocket connected');

        // Send initial configuration
        const config = {
          text: " ",
          voice_settings: {
            stability: 0.5,
            similarity_boost: 0.5,
            style: 0.0,
            use_speaker_boost: true
          }
        };

        wsRef.current?.send(JSON.stringify(config));
      };

      wsRef.current.onmessage = async (event) => {
        try {
          const data: WebSocketMessage = JSON.parse(event.data);
          
          if (data.audio) {
            await playAudioChunk(data.audio);
          }
          
          if (data.isFinal) {
            onAudioEnd?.();
            // Dispatch custom event for external listeners
            window.dispatchEvent(new CustomEvent('elevenlabs-audio-end'));
          }
        } catch (error) {
          console.error('Error processing WebSocket message:', error);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        onError?.('WebSocket connection error');
      };

      wsRef.current.onclose = () => {
        console.log('ElevenLabs WebSocket disconnected');
      };

    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      onError?.('Failed to connect to voice service');
    }
  }, [initializeAudioContext, onError, onAudioEnd]);

  const playAudioChunk = useCallback(async (base64Audio: string) => {
    if (!audioContextRef.current) return;

    try {
      // Decode base64 to array buffer
      const binaryString = atob(base64Audio);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Convert PCM data to AudioBuffer
      const audioBuffer = audioContextRef.current.createBuffer(1, bytes.length / 2, 16000);
      const channelData = audioBuffer.getChannelData(0);
      
      // Convert 16-bit PCM to float32
      for (let i = 0; i < channelData.length; i++) {
        const sample = (bytes[i * 2] | (bytes[i * 2 + 1] << 8));
        channelData[i] = sample < 32768 ? sample / 32768 : (sample - 65536) / 32768;
      }

      // Add to queue and play
      audioQueueRef.current.push(audioBuffer);
      
      if (!isPlayingRef.current) {
        playNextInQueue();
      }

    } catch (error) {
      console.error('Error playing audio chunk:', error);
    }
  }, []);

  const playNextInQueue = useCallback(() => {
    if (audioQueueRef.current.length === 0) {
      isPlayingRef.current = false;
      return;
    }

    if (!audioContextRef.current) return;

    isPlayingRef.current = true;
    const audioBuffer = audioQueueRef.current.shift()!;
    
    const source = audioContextRef.current.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(audioContextRef.current.destination);
    
    source.onended = () => {
      playNextInQueue();
    };
    
    source.start();
    
    if (audioQueueRef.current.length === 1) {
      onAudioStart?.();
      // Dispatch custom event for external listeners
      window.dispatchEvent(new CustomEvent('elevenlabs-audio-start'));
    }
  }, [onAudioStart]);

  const speakText = useCallback((text: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      onError?.('WebSocket not connected');
      return;
    }

    try {
      // Send text to be synthesized
      const message = {
        text: text + " ",
        try_trigger_generation: true
      };
      
      wsRef.current.send(JSON.stringify(message));
      
      // Send end of stream marker
      setTimeout(() => {
        if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
          wsRef.current.send(JSON.stringify({ text: "" }));
        }
      }, 100);

    } catch (error) {
      console.error('Error sending text to WebSocket:', error);
      onError?.('Failed to send text for synthesis');
    }
  }, [onError]);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    
    audioQueueRef.current = [];
    isPlayingRef.current = false;
  }, []);

  // Initialize WebSocket connection
  useEffect(() => {
    connectWebSocket();
    
    return () => {
      disconnect();
    };
  }, [connectWebSocket, disconnect]);

  // Expose speakText function globally
  useEffect(() => {
    (window as any).elevenLabsSpeakText = speakText;
    return () => {
      delete (window as any).elevenLabsSpeakText;
    };
  }, [speakText]);

  return null; // This component doesn't render anything
}

// Utility function for external use
export const speakWithElevenLabs = (text: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (typeof window !== 'undefined' && (window as any).elevenLabsSpeakText) {
      (window as any).elevenLabsSpeakText(text);
      
      // Listen for audio end event
      const handleAudioEnd = () => {
        resolve();
        window.removeEventListener('elevenlabs-audio-end', handleAudioEnd);
      };
      
      window.addEventListener('elevenlabs-audio-end', handleAudioEnd);
      
      // Timeout after 30 seconds
      setTimeout(() => {
        window.removeEventListener('elevenlabs-audio-end', handleAudioEnd);
        reject(new Error('Speech synthesis timeout'));
      }, 30000);
      
    } else {
      reject(new Error('ElevenLabs WebSocket not available'));
    }
  });
};
