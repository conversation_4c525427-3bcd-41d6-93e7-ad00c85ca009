'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

interface AudioHandlerProps {
  isListening: boolean;
  onSpeechResult: (transcript: string) => void;
  onSpeechEnd: () => void;
  onError: (error: string) => void;
}

declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

export default function AudioHandler({
  isListening,
  onSpeechResult,
  onSpeechEnd,
  onError
}: AudioHandlerProps) {
  const recognitionRef = useRef<any>(null);
  const [isSupported, setIsSupported] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (SpeechRecognition) {
      setIsSupported(true);
      
      // Initialize speech recognition
      const recognition = new SpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      recognition.maxAlternatives = 1;

      recognition.onstart = () => {
        console.log('Speech recognition started');
      };

      recognition.onresult = (event: any) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        const fullTranscript = finalTranscript || interimTranscript;
        setCurrentTranscript(fullTranscript);

        if (finalTranscript) {
          onSpeechResult(finalTranscript.trim());
        }
      };

      recognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
        onError(`Speech recognition error: ${event.error}`);
      };

      recognition.onend = () => {
        console.log('Speech recognition ended');
        onSpeechEnd();
        setCurrentTranscript('');
      };

      recognitionRef.current = recognition;
    } else {
      setIsSupported(false);
      onError('Speech recognition is not supported in this browser');
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [onSpeechResult, onSpeechEnd, onError]);

  useEffect(() => {
    if (!isSupported || !recognitionRef.current) return;

    if (isListening) {
      try {
        recognitionRef.current.start();
      } catch (error) {
        console.error('Error starting speech recognition:', error);
      }
    } else {
      try {
        recognitionRef.current.stop();
      } catch (error) {
        console.error('Error stopping speech recognition:', error);
      }
    }
  }, [isListening, isSupported]);

  const speakText = useCallback(async (text: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!('speechSynthesis' in window)) {
        reject(new Error('Speech synthesis not supported'));
        return;
      }

      // Cancel any ongoing speech
      window.speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      
      // Configure voice settings
      utterance.rate = 0.9;
      utterance.pitch = 1.0;
      utterance.volume = 1.0;

      // Try to use a professional-sounding voice
      const voices = window.speechSynthesis.getVoices();
      const preferredVoice = voices.find(voice => 
        voice.name.includes('Alex') || 
        voice.name.includes('Daniel') || 
        voice.name.includes('Google US English')
      );
      
      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }

      utterance.onend = () => {
        resolve();
      };

      utterance.onerror = (event) => {
        reject(new Error(`Speech synthesis error: ${event.error}`));
      };

      window.speechSynthesis.speak(utterance);
    });
  }, []);

  // Expose speakText function to parent components
  useEffect(() => {
    (window as any).pitchpalSpeakText = speakText;
    return () => {
      delete (window as any).pitchpalSpeakText;
    };
  }, [speakText]);

  if (!isSupported) {
    return (
      <div className="fixed bottom-4 right-4 bg-red-600 text-white p-4 rounded-lg shadow-lg">
        <p className="text-sm">
          Speech recognition is not supported in this browser. 
          Please use Chrome, Edge, or Safari for the best experience.
        </p>
      </div>
    );
  }

  return (
    <>
      {/* Live transcript display */}
      {currentTranscript && isListening && (
        <div className="fixed bottom-20 left-4 right-4 bg-black/80 text-white p-4 rounded-lg backdrop-blur-sm">
          <div className="text-sm text-gray-400 mb-1">You're saying:</div>
          <div className="text-white">{currentTranscript}</div>
        </div>
      )}
    </>
  );
}

// Utility function to synthesize speech using ElevenLabs WebSocket streaming
export async function synthesizeWithElevenLabs(text: string): Promise<void> {
  try {
    // Get WebSocket configuration from API
    const response = await fetch('/api/voice', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text }),
    });

    if (!response.ok) {
      throw new Error(`Failed to get WebSocket config: ${response.statusText}`);
    }

    const config = await response.json();

    // Use WebSocket streaming for real-time audio
    await streamAudioWithWebSocket(text, config);

  } catch (error) {
    console.error('ElevenLabs synthesis error:', error);
    // Fallback to browser speech synthesis
    if ('speechSynthesis' in window) {
      return new Promise((resolve, reject) => {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.rate = 0.9;
        utterance.pitch = 1.0;
        utterance.volume = 1.0;
        utterance.onend = () => resolve();
        utterance.onerror = () => reject(new Error('Browser synthesis failed'));
        window.speechSynthesis.speak(utterance);
      });
    }
    throw error;
  }
}

// WebSocket streaming function based on ElevenLabs documentation
async function streamAudioWithWebSocket(text: string, config: any): Promise<void> {
  return new Promise((resolve, reject) => {
    // Add API key to WebSocket URL since headers aren't supported in browser WebSocket
    const wsUrl = `${config.websocket_url}&xi_api_key=${config.api_key}`;
    const websocket = new WebSocket(wsUrl);

    let audioChunks: string[] = [];
    let hasStartedPlaying = false;

    websocket.onopen = () => {
      console.log('ElevenLabs WebSocket connected');

      // Send initial configuration with voice settings
      websocket.send(JSON.stringify({
        text: ' ',
        voice_settings: config.voice_settings,
        generation_config: config.generation_config
      }));

      // Send the actual text
      websocket.send(JSON.stringify({ text: text }));

      // Send empty string to close the connection
      websocket.send(JSON.stringify({ text: '' }));
    };

    websocket.onmessage = async (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('WebSocket message received:', data);

        if (data.audio) {
          audioChunks.push(data.audio);

          // Start playing immediately when we get the first chunk
          if (!hasStartedPlaying) {
            hasStartedPlaying = true;
            playAudioChunks(audioChunks);
          }
        }
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
      }
    };

    websocket.onclose = async () => {
      console.log('ElevenLabs WebSocket closed');

      // Make sure all audio is played
      if (audioChunks.length > 0 && !hasStartedPlaying) {
        await playAudioChunks(audioChunks);
      }

      resolve();
    };

    websocket.onerror = (error) => {
      console.error('WebSocket error:', error);
      reject(new Error('WebSocket connection failed'));
    };

    // Timeout after 30 seconds
    setTimeout(() => {
      if (websocket.readyState === WebSocket.OPEN) {
        websocket.close();
        reject(new Error('WebSocket timeout'));
      }
    }, 30000);
  });
}

// Helper function to play audio chunks as MP3
async function playAudioChunks(base64Chunks: string[]): Promise<void> {
  try {
    // Combine all base64 chunks into one
    const combinedBase64 = base64Chunks.join('');

    // Convert base64 to blob
    const binaryString = atob(combinedBase64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    const audioBlob = new Blob([bytes], { type: 'audio/mpeg' });
    const audioUrl = URL.createObjectURL(audioBlob);

    // Play the audio
    const audio = new Audio(audioUrl);

    return new Promise((resolve, reject) => {
      audio.onended = () => {
        URL.revokeObjectURL(audioUrl);
        resolve();
      };

      audio.onerror = () => {
        URL.revokeObjectURL(audioUrl);
        reject(new Error('Audio playback failed'));
      };

      audio.play().catch(reject);
    });

  } catch (error) {
    console.error('Error playing audio chunks:', error);
    throw error;
  }
}


