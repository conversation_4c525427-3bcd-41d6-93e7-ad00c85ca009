'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

interface AudioHandlerProps {
  isListening: boolean;
  onSpeechResult: (transcript: string) => void;
  onSpeechEnd: () => void;
  onError: (error: string) => void;
}

declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

export default function AudioHandler({
  isListening,
  onSpeechResult,
  onSpeechEnd,
  onError
}: AudioHandlerProps) {
  const recognitionRef = useRef<any>(null);
  const [isSupported, setIsSupported] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (SpeechRecognition) {
      setIsSupported(true);
      
      // Initialize speech recognition
      const recognition = new SpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      recognition.maxAlternatives = 1;

      recognition.onstart = () => {
        console.log('Speech recognition started');
      };

      recognition.onresult = (event: any) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        const fullTranscript = finalTranscript || interimTranscript;
        setCurrentTranscript(fullTranscript);

        if (finalTranscript) {
          onSpeechResult(finalTranscript.trim());
        }
      };

      recognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
        // Only show error for non-network issues to avoid spam
        if (event.error !== 'network') {
          onError(`Speech recognition error: ${event.error}`);
        }
      };

      recognition.onend = () => {
        console.log('Speech recognition ended');
        onSpeechEnd();
        setCurrentTranscript('');
      };

      recognitionRef.current = recognition;
    } else {
      setIsSupported(false);
      onError('Speech recognition is not supported in this browser');
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [onSpeechResult, onSpeechEnd, onError]);

  useEffect(() => {
    if (!isSupported || !recognitionRef.current) return;

    if (isListening) {
      try {
        recognitionRef.current.start();
      } catch (error) {
        console.error('Error starting speech recognition:', error);
      }
    } else {
      try {
        recognitionRef.current.stop();
      } catch (error) {
        console.error('Error stopping speech recognition:', error);
      }
    }
  }, [isListening, isSupported]);

  const speakText = useCallback(async (text: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!('speechSynthesis' in window)) {
        reject(new Error('Speech synthesis not supported'));
        return;
      }

      // Cancel any ongoing speech
      window.speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      
      // Configure voice settings
      utterance.rate = 0.9;
      utterance.pitch = 1.0;
      utterance.volume = 1.0;

      // Try to use a professional-sounding voice
      const voices = window.speechSynthesis.getVoices();
      const preferredVoice = voices.find(voice => 
        voice.name.includes('Alex') || 
        voice.name.includes('Daniel') || 
        voice.name.includes('Google US English')
      );
      
      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }

      utterance.onend = () => {
        resolve();
      };

      utterance.onerror = (event) => {
        reject(new Error(`Speech synthesis error: ${event.error}`));
      };

      window.speechSynthesis.speak(utterance);
    });
  }, []);

  // Expose speakText function to parent components
  useEffect(() => {
    (window as any).pitchpalSpeakText = speakText;
    return () => {
      delete (window as any).pitchpalSpeakText;
    };
  }, [speakText]);

  if (!isSupported) {
    return (
      <div className="fixed bottom-4 right-4 bg-red-600 text-white p-4 rounded-lg shadow-lg">
        <p className="text-sm">
          Speech recognition is not supported in this browser. 
          Please use Chrome, Edge, or Safari for the best experience.
        </p>
      </div>
    );
  }

  return (
    <>
      {/* Live transcript display */}
      {currentTranscript && isListening && (
        <div className="fixed bottom-20 left-4 right-4 bg-black/80 text-white p-4 rounded-lg backdrop-blur-sm">
          <div className="text-sm text-gray-400 mb-1">You're saying:</div>
          <div className="text-white">{currentTranscript}</div>
        </div>
      )}
    </>
  );
}

// Utility function to synthesize speech using ElevenLabs WebSocket streaming via SSE
export async function synthesizeWithElevenLabs(text: string): Promise<void> {
  console.log('Starting ElevenLabs synthesis for text:', text);

  try {
    // Use Server-Sent Events to stream from our WebSocket proxy
    await streamAudioWithSSE(text);

  } catch (error) {
    console.error('ElevenLabs synthesis error:', error);
    console.log('Falling back to browser speech synthesis...');

    // Fallback to browser speech synthesis
    if ('speechSynthesis' in window) {
      return new Promise((resolve, reject) => {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.rate = 0.9;
        utterance.pitch = 1.0;
        utterance.volume = 1.0;
        utterance.onend = () => resolve();
        utterance.onerror = () => reject(new Error('Browser synthesis failed'));
        window.speechSynthesis.speak(utterance);
      });
    }
    throw error;
  }
}



// Server-Sent Events streaming function for ElevenLabs WebSocket proxy
async function streamAudioWithSSE(text: string): Promise<void> {
  return new Promise((resolve, reject) => {
    console.log('Starting SSE stream for ElevenLabs WebSocket...');

    const eventSource = new EventSource(`/api/voice-ws?text=${encodeURIComponent(text)}`);
    let audioChunks: string[] = [];

    eventSource.onmessage = async (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('SSE message received:', data);
        console.log('Message keys:', Object.keys(data));
        console.log('Full message content:', JSON.stringify(data, null, 2));
        console.log('Has audio property:', !!data.audio);

        if (data.audio) {
          console.log('Audio chunk received, length:', data.audio.length);
          audioChunks.push(data.audio);
          console.log('Total chunks collected:', audioChunks.length);

          // Don't start playing immediately, collect all chunks first
          // We'll play when the stream ends
        } else {
          console.log('No audio property in message');
          // Check if there's an error in the response
          if (data.error) {
            console.error('ElevenLabs WebSocket error:', data.error);
            eventSource.close();
            reject(new Error(`ElevenLabs error: ${data.error}`));
            return;
          }
        }
      } catch (error) {
        console.error('Error processing SSE message:', error);
      }
    };

    eventSource.onopen = () => {
      console.log('SSE connection opened');
    };

    eventSource.onerror = (error) => {
      console.error('SSE error:', error);
      eventSource.close();

      // Play all collected audio chunks when stream ends
      if (audioChunks.length > 0) {
        console.log('Playing', audioChunks.length, 'audio chunks on stream end');
        playAudioChunks(audioChunks).then(() => {
          console.log('Audio playback completed');
          resolve();
        }).catch((playError) => {
          console.error('Audio playback failed:', playError);
          reject(playError);
        });
      } else {
        // Check if this is a normal close or an actual error
        if (eventSource.readyState === EventSource.CLOSED) {
          console.log('SSE connection closed normally - no audio received');
          resolve();
        } else {
          reject(new Error('SSE connection failed'));
        }
      }
    };

    // Timeout after 30 seconds
    setTimeout(() => {
      if (eventSource.readyState === EventSource.OPEN) {
        eventSource.close();
        reject(new Error('SSE timeout'));
      }
    }, 30000);
  });
}

// Helper function to play audio chunks as MP3
async function playAudioChunks(base64Chunks: string[]): Promise<void> {
  try {
    // Combine all base64 chunks into one
    const combinedBase64 = base64Chunks.join('');

    // Convert base64 to blob
    const binaryString = atob(combinedBase64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    const audioBlob = new Blob([bytes], { type: 'audio/mpeg' });
    const audioUrl = URL.createObjectURL(audioBlob);

    // Play the audio
    const audio = new Audio(audioUrl);

    return new Promise((resolve, reject) => {
      audio.onended = () => {
        URL.revokeObjectURL(audioUrl);
        resolve();
      };

      audio.onerror = () => {
        URL.revokeObjectURL(audioUrl);
        reject(new Error('Audio playback failed'));
      };

      audio.play().catch(reject);
    });

  } catch (error) {
    console.error('Error playing audio chunks:', error);
    throw error;
  }
}


