'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import ElevenLabsWebSocket, { speakWithElevenLabs } from './ElevenLabsWebSocket';

interface AudioHandlerProps {
  isListening: boolean;
  isAISpeaking: boolean;
  onSpeechResult: (transcript: string) => void;
  onSpeechEnd: () => void;
  onError: (error: string) => void;
  onAudioStart?: () => void;
  onAudioEnd?: () => void;
}

declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

export default function AudioHandler({
  isListening,
  isAISpeaking,
  onSpeechResult,
  onSpeechEnd,
  onError,
  onAudioStart,
  onAudioEnd
}: AudioHandlerProps) {
  const recognitionRef = useRef<any>(null);
  const [isSupported, setIsSupported] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (SpeechRecognition) {
      setIsSupported(true);
      
      // Initialize speech recognition
      const recognition = new SpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      recognition.maxAlternatives = 1;

      recognition.onstart = () => {
        console.log('Speech recognition started');
      };

      recognition.onresult = (event: any) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        const fullTranscript = finalTranscript || interimTranscript;
        setCurrentTranscript(fullTranscript);

        if (finalTranscript) {
          onSpeechResult(finalTranscript.trim());
        }
      };

      recognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
        onError(`Speech recognition error: ${event.error}`);
      };

      recognition.onend = () => {
        console.log('Speech recognition ended');
        onSpeechEnd();
        setCurrentTranscript('');
      };

      recognitionRef.current = recognition;
    } else {
      setIsSupported(false);
      onError('Speech recognition is not supported in this browser');
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [onSpeechResult, onSpeechEnd, onError]);

  useEffect(() => {
    if (!isSupported || !recognitionRef.current) return;

    if (isListening) {
      try {
        recognitionRef.current.start();
      } catch (error) {
        console.error('Error starting speech recognition:', error);
      }
    } else {
      try {
        recognitionRef.current.stop();
      } catch (error) {
        console.error('Error stopping speech recognition:', error);
      }
    }
  }, [isListening, isSupported]);

  const speakText = useCallback(async (text: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!('speechSynthesis' in window)) {
        reject(new Error('Speech synthesis not supported'));
        return;
      }

      // Cancel any ongoing speech
      window.speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      
      // Configure voice settings
      utterance.rate = 0.9;
      utterance.pitch = 1.0;
      utterance.volume = 1.0;

      // Try to use a professional-sounding voice
      const voices = window.speechSynthesis.getVoices();
      const preferredVoice = voices.find(voice => 
        voice.name.includes('Alex') || 
        voice.name.includes('Daniel') || 
        voice.name.includes('Google US English')
      );
      
      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }

      utterance.onend = () => {
        resolve();
      };

      utterance.onerror = (event) => {
        reject(new Error(`Speech synthesis error: ${event.error}`));
      };

      window.speechSynthesis.speak(utterance);
    });
  }, []);

  // Expose speakText function to parent components
  useEffect(() => {
    (window as any).pitchpalSpeakText = speakText;
    return () => {
      delete (window as any).pitchpalSpeakText;
    };
  }, [speakText]);

  if (!isSupported) {
    return (
      <div className="fixed bottom-4 right-4 bg-red-600 text-white p-4 rounded-lg shadow-lg">
        <p className="text-sm">
          Speech recognition is not supported in this browser. 
          Please use Chrome, Edge, or Safari for the best experience.
        </p>
      </div>
    );
  }

  return (
    <>
      {/* ElevenLabs WebSocket Component */}
      <ElevenLabsWebSocket
        onAudioStart={onAudioStart}
        onAudioEnd={onAudioEnd}
        onError={onError}
      />

      {/* Live transcript display */}
      {currentTranscript && isListening && (
        <div className="fixed bottom-20 left-4 right-4 bg-black/80 text-white p-4 rounded-lg backdrop-blur-sm">
          <div className="text-sm text-gray-400 mb-1">You're saying:</div>
          <div className="text-white">{currentTranscript}</div>
        </div>
      )}
    </>
  );
}

// Utility function to synthesize speech using ElevenLabs WebSocket
export async function synthesizeWithElevenLabs(text: string): Promise<void> {
  try {
    // Use the new WebSocket implementation for real-time streaming
    await speakWithElevenLabs(text);
  } catch (error) {
    console.error('ElevenLabs WebSocket synthesis error:', error);

    // Fallback to browser speech synthesis
    if ('speechSynthesis' in window) {
      return new Promise((resolve, reject) => {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.rate = 0.9;
        utterance.pitch = 1.0;
        utterance.volume = 1.0;

        // Try to use a professional voice
        const voices = window.speechSynthesis.getVoices();
        const preferredVoice = voices.find(voice =>
          voice.name.includes('Alex') ||
          voice.name.includes('Daniel') ||
          voice.name.includes('Google US English')
        );

        if (preferredVoice) {
          utterance.voice = preferredVoice;
        }

        utterance.onend = () => resolve();
        utterance.onerror = () => reject(new Error('Browser synthesis failed'));
        window.speechSynthesis.speak(utterance);
      });
    }
    throw error;
  }
}

// Utility function to play audio from URL
export function playAudio(audioUrl: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const audio = new Audio(audioUrl);
    
    audio.onended = () => resolve();
    audio.onerror = () => reject(new Error('Audio playback failed'));
    
    audio.play().catch(reject);
  });
}
