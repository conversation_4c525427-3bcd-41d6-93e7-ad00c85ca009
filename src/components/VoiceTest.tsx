'use client';

import { useState } from 'react';
import { speakWithElevenLabs } from './ElevenLabsWebSocket';

export default function VoiceTest() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [testText, setTestText] = useState("Hello! This is a test of the ElevenLabs WebSocket integration.");

  const handleTest = async () => {
    if (isPlaying) return;
    
    setIsPlaying(true);
    try {
      await speakWithElevenLabs(testText);
    } catch (error) {
      console.error('Voice test error:', error);
    } finally {
      setIsPlaying(false);
    }
  };

  return (
    <div className="fixed top-4 right-4 bg-white/10 backdrop-blur-lg rounded-lg p-4 border border-white/20 z-50">
      <h3 className="text-white font-semibold mb-2">Voice Test</h3>
      <textarea
        value={testText}
        onChange={(e) => setTestText(e.target.value)}
        className="w-full p-2 bg-white/10 border border-white/20 rounded text-white text-sm mb-2"
        rows={3}
        placeholder="Enter text to test..."
      />
      <button
        onClick={handleTest}
        disabled={isPlaying}
        className={`w-full px-3 py-2 rounded text-sm font-medium ${
          isPlaying 
            ? 'bg-gray-600 text-gray-300 cursor-not-allowed' 
            : 'bg-blue-600 hover:bg-blue-700 text-white'
        }`}
      >
        {isPlaying ? 'Playing...' : 'Test Voice'}
      </button>
    </div>
  );
}
