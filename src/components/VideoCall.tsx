'use client';

import { useRef, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface VideoCallProps {
  isActive: boolean;
  isListening: boolean;
  isAISpeaking: boolean;
  currentQuestion: string;
  onStartCall: () => void;
  onEndCall: () => void;
}

export default function VideoCall({
  isActive,
  isListening,
  isAISpeaking,
  currentQuestion,
  onStartCall,
  onEndCall
}: VideoCallProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  useEffect(() => {
    initializeCamera();
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const initializeCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
      
      setStream(mediaStream);
      setHasPermission(true);
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      setHasPermission(false);
    }
  };

  if (hasPermission === false) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <div className="text-6xl mb-4">📹</div>
          <h2 className="text-2xl font-bold mb-4">Camera Access Required</h2>
          <p className="text-gray-400 mb-6">
            Please allow camera and microphone access to start your pitch practice.
          </p>
          <button
            onClick={initializeCamera}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg"
          >
            Grant Access
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black flex flex-col">
      {/* Main video area */}
      <div className="flex-1 flex">
        {/* User video */}
        <div className="flex-1 relative">
          <video
            ref={videoRef}
            autoPlay
            muted
            playsInline
            className="w-full h-full object-cover"
          />
          
          {/* User label */}
          <div className="absolute bottom-4 left-4 bg-black/70 text-white px-3 py-1 rounded-lg backdrop-blur-sm">
            You
          </div>
          
          {/* Listening indicator */}
          <AnimatePresence>
            {isListening && (
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.8, opacity: 0 }}
                className="absolute top-4 left-4 bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-2"
              >
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ repeat: Infinity, duration: 1 }}
                  className="w-2 h-2 bg-white rounded-full"
                />
                <span>Listening...</span>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* AI/VC video area */}
        <div className="flex-1 relative bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
          <div className="text-center text-white">
            {/* AI Avatar */}
            <motion.div
              animate={isAISpeaking ? { scale: [1, 1.05, 1] } : {}}
              transition={{ repeat: isAISpeaking ? Infinity : 0, duration: 2 }}
              className="w-40 h-40 bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center mb-6 mx-auto shadow-2xl"
            >
              <span className="text-6xl font-bold">K</span>
            </motion.div>
            
            <h3 className="text-2xl font-semibold mb-2">Kenard (CEO)</h3>
            <p className="text-gray-400 text-sm mb-4">Venture Capital Partner</p>
            
            {/* Speaking indicator */}
            <AnimatePresence>
              {isAISpeaking && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  className="flex items-center justify-center space-x-1"
                >
                  <div className="text-green-400 text-sm font-medium">Speaking</div>
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ repeat: Infinity, duration: 0.8 }}
                    className="w-2 h-2 bg-green-400 rounded-full"
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          
          {/* VC label */}
          <div className="absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded-lg backdrop-blur-sm">
            Kenard (CEO)
          </div>
        </div>
      </div>

      {/* Question display */}
      <AnimatePresence>
        {currentQuestion && isActive && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            className="bg-gray-900/95 backdrop-blur-sm p-6 border-t border-gray-700"
          >
            <div className="max-w-4xl mx-auto text-center">
              <p className="text-white text-lg font-medium">{currentQuestion}</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Start call overlay */}
      <AnimatePresence>
        {!isActive && hasPermission && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center"
          >
            <div className="text-center">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="mb-8"
              >
                <h2 className="text-3xl font-bold text-white mb-4">
                  Ready to Start Your Pitch?
                </h2>
                <p className="text-gray-300 text-lg">
                  You'll be interviewed by Kenard, an experienced VC partner
                </p>
              </motion.div>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onStartCall}
                className="bg-green-600 hover:bg-green-700 text-white text-xl font-semibold px-12 py-4 rounded-xl shadow-lg transition-colors"
              >
                Start Call
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
